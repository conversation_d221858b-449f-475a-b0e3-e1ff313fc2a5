<div class="product-form {% render 'class-settings' settings:settings prefix:'variant_selector_class' %}{% if type contains 'table' %} variant-selector--table{% endif %}">
  {% liquid
    assign handle = settings.product | default: product.handle
    if handle != product.handle
      assign product = all_products[handle]
      render 'product-data' product:product, script_tag:true, preselect_variant:false history_state:settings.history_state, include:'variants,inventory,media,shipment_date', metafields:section.settings.metafields
    endif

    assign option_exclude = settings.option_exclude | newline_to_br | split: '<br />' | json

    assign field_buttons_colors_wrap = settings.color_option_wrap_mobile | default: false
  %}

  {% render 'product-siblings' product:product settings:settings %}

  <div
    x-data="{
    productData: $store.products['{{ product.handle }}'],
    {% if type contains 'table' %}sizeMatrix: {% render 'variant-table-data' %}{% endif %}
    }"
    x-init="() => {
      if ($store.products['{{ product.handle }}'].variants.length == 1) Products.select.variant('{{ product.handle }}', $store.products['{{ product.handle }}'].variants[0].id)
    }"
  >
    <script>
      function removeApostrophe(value) {
        return value.replace(/'/g, "");
      }
    </script>

    <div class="hidden">
      <label>
        <select name="id" class="w-full p-3 border" aria-label="variant-selector">
          {% for variant in product.variants %}
            <option value="{{ variant.id }}" {% unless variant.available %}disabled{% endunless %}>{{ variant.title }}</option>
          {% endfor %}
        </select>
      </label>
      <label>
        <input type="submit" value="{{ 'products.product.add_to_cart' | t }}">
      </label>
    </div>

    <div class="product-form__options">
      {% assign ck_single_size_variant = false %}
      {% if hide_single_size_variant %}
        {% assign ck_single_size_variant = hide_single_size_variant %}
      {% endif %}

      {%- if type contains 'table' -%}
        {% assign matrix_keys = settings.size_matrix_key | split: ':' %}
        <script data-size-matrix type="application/json">
          {
            "settings": {{ shop.metaobjects[matrix_keys.first][matrix_keys.last] | json }},
            "sizes": {{ shop.metaobjects[matrix_keys.first][matrix_keys.last].sizes.value | json }}
          }
        </script>
      {%- endif -%}

      <template x-for="(option, oi) in $store.products['{{ product.handle }}'].options" :key="`${option.name}_${option.selected_value}_${JSON.stringify(option.values)}`">

        <div
          x-init="{% if settings.enable_logs %}console.log('ck_single_size_variant', {{ck_single_size_variant}}){% endif %}"
          :class="{
              [`product-form__option product-form__option--${option.name.handle()}`]: true,
              'hidden': {{ck_single_size_variant | json }} && option.name.handle() === 'size' && option.values.length === 1
            }"
          {% if settings.option_exclude != blank %}
            x-show="!{{ option_exclude | replace: '"', "'" | default: '[]' }}.includes(option.name)"
          {% endif %}
          {%- if settings.variant_segment_design == 'tabs' -%}
            x-data="{
              get activeTab() {
                return $store.colorTabs.activeTab;
              },
              get currentProduct() {
                return $store.products['{{ product.handle }}'];
              },
              get colorGroups() {
                return this.currentProduct?.color_groups?.length > 0
                  ? this.currentProduct.color_groups
                  : Object.values($store.products || {}).find(p => p.color_groups?.length > 0)?.color_groups || [];
              },
              get colorOption() {
                return this.currentProduct?.options?.find(opt => opt.name.handle() === 'color');
              },
              maxTabs: {{ settings.variant_segment_max_tabs | default: 4 }},
              allTabLabel: '{{ settings.variant_segment_all | default: 'All' }}',
              hasColorGroups() {
                return this.colorGroups.length > 0;
              },
              isProductInGroup(productId, group) {
                return group.products?.some(id =>
                  productId === id || productId === parseInt(id) || productId === String(id)
                );
              },
              groupHasSwatches(group) {
                if (!group.products?.length || !this.colorOption?.values) return false;
                return this.colorOption.values.some(value => {
                  const valueProduct = window.products[value.product];
                  return valueProduct && this.isProductInGroup(valueProduct.id, group);
                });
              },
              getVisibleTabs() {
                const sortedGroups = this.colorGroups.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
                return sortedGroups.filter(group => this.groupHasSwatches(group)).slice(0, this.maxTabs);
              },
              switchTab(tabHandle) {
                $store.colorTabs.setActiveTab(tabHandle);
                this.$dispatch('tab-changed', { tab: tabHandle });
              },
              isTabActive(tabHandle) {
                return this.activeTab === tabHandle;
              },
              shouldShowSwatch(value) {
                // Non-color options always show
                if (option.name.handle() !== 'color') return true;

                // If no color groups exist, show all swatches (fallback behavior)
                if (!this.hasColorGroups()) return true;

                const valueProduct = window.products[value.product];
                if (!valueProduct) return false;

                // For 'all' tab, only show swatches that belong to at least one group
                if (this.activeTab === 'all') {
                  return this.colorGroups.some(group => this.isProductInGroup(valueProduct.id, group));
                }

                // For specific tabs, check if swatch belongs to active group
                const activeGroup = this.colorGroups.find(group => group.handle === this.activeTab);
                return activeGroup && this.isProductInGroup(valueProduct.id, activeGroup);
              }
            }"
          {%- else -%}
            x-data="{
              shouldShowSwatch(value) {
                // For non-tabs designs, always show all swatches
                return true;
              }
            }"
          {%- endif -%}
        >

          {%- if settings.variant_segment_design == 'tabs' -%}
            <!-- Tabs Navigation (above swatches) -->
            <div class="variant-tab" x-show="option.name.handle() === 'color' && hasColorGroups()" x-cloak>
              <div class="variant-tabs__nav tabs">
                <!-- All Tab -->
                <button
                  type="button"
                  class="variant-tabs__tab"
                  :class="{ 'active': isTabActive('all') }"
                  @click="switchTab('all')"
                  x-text="allTabLabel"
                ></button>

                <!-- Color Group Tabs -->
                <template x-for="group in getVisibleTabs()" :key="group.handle">
                  <button
                    type="button"
                    class="variant-tabs__tab"
                    :class="{ 'active': isTabActive(group.handle) }"
                    @click="switchTab(group.handle)"
                    x-text="group.name"
                  ></button>
                </template>
              </div>
            </div>
          {% endif %}

          <!-- Show label wrapper normally -->
          <div class="product-form__option-label-wrapper">


            <template x-if="option.name == 'Color'">
                <p class="product-form__option-selected type-item">
                  {%- if settings.variant_segment_design == 'list' -%}
                    <!-- List Design: First span shows segment, second shows selected color -->
                    <span x-show="!!Object.values(products).find(p => p.metafields['{{ settings.variant_segment }}'])">{{ settings.variant_segment_default }}: </span>
                  {%- elsif settings.variant_segment_design == 'tabs' -%}
                    <!-- Tabs Design: First span shows group of selected swatch, second shows selected color -->
                    <span x-text="(() => {
                      const selectedValue = $store.products['{{ product.handle }}'].options[oi].selected_value;
                      if (!selectedValue || !colorGroups || colorGroups.length === 0) return '';

                      // Find the value object that matches the selected value
                      const valueObj = $store.products['{{ product.handle }}'].options[oi].values.find(v => v.value === selectedValue);
                      if (!valueObj || !valueObj.product) return '';

                      // Get the product associated with this selected color value
                      const valueProduct = window.products[valueObj.product];
                      if (!valueProduct) return '';

                      // Find which color group contains this product
                      const group = colorGroups.find(group =>
                        group.products && group.products.some(productId =>
                          valueProduct.id === productId ||
                          valueProduct.id === parseInt(productId) ||
                          valueProduct.id === String(productId)
                        )
                      );

                      return group ? group.name + ': ' : '';
                    })()"></span>
                  {%- endif -%}
                  <span x-text="(() => {
                    {%- if settings.variant_segment_design == 'list' -%}
                      // For list segments design, only show selected value if it belongs to default segment (no segment)
                      const selectedValue = $store.products['{{ product.handle }}'].options[oi].selected_value;
                      if (!selectedValue) return '';

                      // Find the value object that matches the selected value
                      const colorOption = $store.products['{{ product.handle }}'].options[oi];
                      const valueObj = colorOption.values.find(v => v.value === selectedValue);
                      if (!valueObj || !valueObj.product) return '';

                      // Get the product associated with this selected color value
                      const valueProduct = window.products[valueObj.product];
                      if (!valueProduct) return '';

                      // Check if this product has no segment (belongs to default)
                      const productSegment = valueProduct.metafields ? valueProduct.metafields['{{ settings.variant_segment }}'] : null;
                      if (!productSegment) {
                        // Product belongs to default segment, show selected value here
                        return oi === 0 ? (colorOption.hover || selectedValue || '') : (selectedValue || '');
                      } else {
                        // Product belongs to specific segment, don't show here
                        return '';
                      }
                    {%- else -%}
                      // For other designs, show selected value normally
                      return oi === 0 ? ($store.products['{{ product.handle }}'].options[oi].hover || $store.products['{{ product.handle }}'].options[oi].selected_value || `{{ 'products.product.option_selection_prompt' | t }} ${option.name}`) : ($store.products['{{ product.handle }}'].options[oi].selected_value || `{{ 'products.product.option_selection_prompt' | t }} ${option.name}`);
                    {%- endif -%}
                  })()"></span>
                </p>
            </template>
            <template x-if="option.name != 'Color'">
                <p class="product-form__option-selected type-item"
                  x-text="oi === 0 ? (option.hover || option.selected_value || `{{ 'products.product.option_selection_prompt' | t }} ${option.name}`) : (`{{ 'products.product.option_selection_prompt' | t }} ${option.name}`)">
                </p>
            </template>

            <div x-show="option.name.handle() == 'size'">{% if settings.disabled != true %}{% render 'button' settings:settings, attributes:settings.shopify_attributes %}{% endif %}</div>
          </div>

          <!-- Main swatches container with conditional visibility -->
          <div class="field__buttons field__buttons--colors"
          x-bind:class="{ '{% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}field__buttons--colors-swatch{% endif %}': option.name.handle() === 'color','field__buttons--colors-wrap': (option.name.handle() === 'color' && {{ field_buttons_colors_wrap }}) }">

            {%- if type contains 'table' -%}
              {% render 'variant-table-column-headers' %}
            {%- endif -%}

            <!-- Single set of swatches with conditional visibility for color options only -->
            <template x-for="value in option.values" :key="value.value + '_' + $store.products['{{ product.handle }}'].id">
              <div class="contents"
                   x-show="option.name.handle() !== 'color' || shouldShowSwatch(value)">
                {% render 'variant-color-option', settings: settings, handle: handle, field_buttons_colors_wrap: field_buttons_colors_wrap, type:type %}
              </div>
            </template>
          </div>

          {%- if settings.variant_segment != blank and settings.variant_segment_design == 'list' -%}
            <!-- List Variant Segment Design (Original) -->
            <template x-for="segment in Util.unique(Object.values(window.products).map((p) => p.metafields['{{ settings.variant_segment }}']))">
              <div class="[&:has(.field\_\_buttons:empty)]:hidden {% render 'class-settings' settings:settings prefix: 'variant_segment_class' %}">
                <div class="product-form__option-label-wrapper">
                  <p class="product-form__option-selected type-item">
                    <span x-text="segment + ':'"></span>
                    <span x-text="(() => {
                      const selectedValue = $store.products['{{ product.handle }}'].options.find(opt => opt.name === 'Color')?.selected_value;
                      if (!selectedValue) return '';

                      // Find the value object that matches the selected value
                      const colorOption = $store.products['{{ product.handle }}'].options.find(opt => opt.name === 'Color');
                      if (!colorOption) return '';

                      const valueObj = colorOption.values.find(v => v.value === selectedValue);
                      if (!valueObj || !valueObj.product) return '';

                      // Get the product associated with this selected color value
                      const valueProduct = window.products[valueObj.product];
                      if (!valueProduct) return '';

                      // Check if this product's segment matches the current segment
                      const productSegment = valueProduct.metafields && valueProduct.metafields['{{ settings.variant_segment }}'];
                      if (productSegment === segment) {
                        return selectedValue;
                      }

                      return '';
                    })()"></span>
                  </p>
                </div>
                {% assign field_buttons_colors_wrap_segment = settings.color_option_wrap_mobile | default: false %}
                <div class="field__buttons field__buttons--colors" x-bind:class="{ 'field__buttons--colors-wrap': (option.name.handle() === 'color' && {{ field_buttons_colors_wrap_segment }}) }" :variant-segment="segment"></div>
              </div>
            </template>
          {%- endif -%}

        </div>
      </template>

    </div>

  </div>

</div>

{% style %}
  @media screen and (min-width: 1024px){
    .field__buttons--colors.field__buttons{
      {% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}
      grid-template-columns: repeat({{ settings.variants_displayed_on_desktop }}, minmax(0, 42px));
      {% else %}
      grid-template-columns: repeat({{ settings.variants_displayed_on_desktop }}, minmax(0, 1fr));
      {% endif %}
    }
    {% if settings.product_color_hover_info %}
      .product-form__option--color .field__buttons--colors label.field__button{
          overflow:visible;
      }
      .product-form__option--color .field__buttons--colors {
        overflow:visible !important;
      }
      .product-form__options .product-form__option--color{
        overflow:visible
      }
    {% endif %}
  }
{% endstyle %}

<script>
  {% for block in section.blocks %}
    {% if block.settings.variants_displayed_on_mobile %}
      window.variants_displayed_on_mobile = '{{ block.settings.variants_displayed_on_mobile }}';
      {% if settings.enable_logs %}console.log('window', window.variants_displayed_on_mobile);{% endif %}
    {% endif %}
  {% endfor %}

  // Alpine.js store for color tabs
  document.addEventListener('alpine:init', () => {
    Alpine.store('colorTabs', {
      userSelectedTab: null, // Only store user interactions

      get activeTab() {
        // If user manually selected a tab, use that
        if (this.userSelectedTab) return this.userSelectedTab;

        // Otherwise calculate based on setting
        {% if settings.variant_segment_default_tab == 'all' %}
          return 'all';
        {% elsif settings.variant_segment_default_tab == 'plp_selection' %}
          const product = window.products['{{ product.handle }}'];
          if (product && product.variant && product.color_groups?.length > 0) {
            const currentVariantProduct = window.products[product.variant.product];
            if (currentVariantProduct) {
              const matchingGroup = product.color_groups.find(group =>
                group.products?.some(id =>
                  currentVariantProduct.id === id ||
                  currentVariantProduct.id === parseInt(id) ||
                  currentVariantProduct.id === String(id)
                )
              );
              if (matchingGroup) return matchingGroup.handle;
            }
          }
          return 'all';
        {% elsif settings.variant_segment_default_tab == 'first' %}
          const product = window.products['{{ product.handle }}'];
          if (product && product.color_groups?.length > 0) {
            const colorOption = product.options?.find(opt => opt.name.handle() === 'color');
            if (colorOption) {
              const sortedGroups = product.color_groups.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
              const firstGroupWithSwatches = sortedGroups.find(group => {
                return colorOption.values.some(value => {
                  const valueProduct = window.products[value.product];
                  return valueProduct && group.products?.some(id =>
                    valueProduct.id === id ||
                    valueProduct.id === parseInt(id) ||
                    valueProduct.id === String(id)
                  );
                });
              });
              if (firstGroupWithSwatches) return firstGroupWithSwatches.handle;
            }
          }
          return 'all';
        {% else %}
          return 'all';
        {% endif %}
      },

      setActiveTab(tab) {
        this.userSelectedTab = tab;
      }
    });
  });

  document.addEventListener("click", function(e){
    if (e.target.tagName.toLowerCase() === 'img') {
      const target = e.target.closest('.field__button');
      {% if settings.enable_logs %}console.log('target', e.target);{% endif %}
      if(target){
        target.addEventListener('click', function() {
          const fieldButtons = target.closest('.field__buttons');
          const boxWidth = fieldButtons.offsetWidth;
          const boxCenter = boxWidth / 2;

          const buttonWidth = target.offsetWidth;
          const buttonOffset = target.offsetLeft + (buttonWidth / 2);
          const scrollAmount = buttonOffset - boxCenter;

          fieldButtons.scrollTo({
            left: scrollAmount,
            behavior: 'smooth'
          });
        });
      }
    }
  });
</script>