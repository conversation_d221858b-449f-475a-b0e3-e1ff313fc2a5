/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "brand_name": "melin",
    "store_name": "melin-brand",
    "returns_url": "https://melin.com/returns",
    "wishlist_url": "/account?view=wishlist",
    "create_an_account_image": "shopify://shop_images/sign-bkgd.webp",
    "sign_in_image": "shopify://shop_images/sign-bkgd.webp",
    "login_text_color": "#000000",
    "login_title": "",
    "login_subtitle": "",
    "login_link_title": "Create Account",
    "login_url": "/account/register",
    "reset_image": "shopify://shop_images/sign-bkgd.webp",
    "rewards_callout_image": "shopify://shop_images/logo-hh_stacked-gold_25c657ef-09cb-42f7-8521-865918993e4f.webp",
    "rewards_callout_image_mobile": "shopify://shop_images/logo-hh_horizontal-gold_b1e4e947-c7b1-4855-aba1-97c4d5cac401.webp",
    "rewards_callout_message": "",
    "favicon": "shopify://shop_images/64x64_melin_favicon.png",
    "social_facebook_link": "https://www.facebook.com/MelinBrand/",
    "social_instagram_link": "https://www.instagram.com/melin/",
    "social_pinterest_link": "https://www.pinterest.com/MelinBrand/",
    "social_twitter_link": "https://twitter.com/melinbrand",
    "social_youtube_link": "https://www.youtube.com/user/melinbrand",
    "enable_custom_meta_title": true,
    "custom_meta_title": "👋 Come Back Soon!",
    "product_item_title_source": "product.title.split(' - ')[0]",
    "product_item_subtitle_source": "product.title.split(' - ')[1]",
    "product_item_type_source": "window.productMetafields?.style?.[product.handle]?.subtitle?.[0] || product.product_type",
    "product_item_enhanced_data": true,
    "product_item_enhanced_data_key": "49e50a0f4ee22321e4a08a3a645b82c9",
    "quickadd_title_source": "product.title.split(' - ')[0]",
    "quickadd_subtitle_source": "product.title.split(' - ')[1]",
    "peripherals_map": "{\n    archipelago:{\n        '2.properties.birthday':'profiles.0.birthday',\n\n'2.properties.email':'identity.email',\n        '2.properties.email':'profiles.0.email',\n\n'2.properties.phone':'identity.phone',           \n        '2.properties.phone':'profiles.0.phone',\n        \n        '2.properties.first_name':'identity.first_name',\n        '2.properties.first_name':'profiles.0.first_name',  \n        '2.properties.last_name':'identity.last_name',\n        '2.properties.last_name':'profiles.0.last_name',\n        '2.properties.favorite_colors':'profiles.0.favorite_colors',\n        '2.properties.favorite_interests':'profiles.0.favorite_interests',\n    \n        '2.properties.shoe_gender':'profiles.0.shoe_gender',\n        '2.properties.sandal_size':'profiles.0.sandal_size',\n        '2.properties.shoe_size':'profiles.0.shoe_size',\n        '2.properties.size':'profiles.0.size',\n        '2.properties.state':'profiles.0.state',\n        '2.properties.secondary_first_name':'profiles.1.first_name',  \n        '2.properties.secondary_last_name':'profiles.1.last_name',\n        '2.properties.secondary_shoe_gender':'profiles.1.shoe_gender',\n        '2.properties.secondary_sandal_size':'profiles.1.sandal_size',\n        '2.properties.secondary_shoe_size':'profiles.1.shoe_size',\n        '2.properties.loyalty_id':'loyalty.loyalty_id',\n        '2.properties.loyalty_status':'loyalty.loyalty_status',\n        '2.properties.pending_point_balance':'loyalty.pending_point_balance',\n        '2.properties.points_balance':'loyalty.points_balance',\n        '2.properties.points_till_next_tier':'loyalty.points_till_next_tier',\n        '2.properties.points_to_maintain_current_tier':'points_to_maintain_current_tier',\n        '2.properties.profile_completed':'profile_completed',\n        '2.properties.tier':'tier'\n    \n    }}",
    "mparticle_enable": true,
    "mparticle_domain_name": "melin.com",
    "key_mparticle": "us2-d2cc12edbff96c4ba01f86a7212eca58",
    "is_development_mode": false,
    "mparticle_config": "v1SecureServiceUrl: 'mparticle.melin.com/webevents/v1/JS/',\n        v2SecureServiceUrl: 'mparticle.melin.com/webevents/v2/JS/',\n        v3SecureServiceUrl: 'mparticle.melin.com/webevents/v3/JS/',\n        configUrl: 'mparticle.melin.com/tags/JS/v2/',\n        identityUrl: 'mparticle.melin.com/identity/v1/',\n        aliasUrl: 'mparticle.melin.com/webevents/v1/identity/',",
    "klaviyo_new_registration_check": "document.addEventListener('DOMContentLoaded', function(event) {\n  if(localStorage.getItem('registration_form_submit')){\n    console.log('local load')\n    let registrationEmail = localStorage.getItem('registration_form_submit')\n    const common_email_source = 'Subscribe to Newsletter via Registration'\n    const common_subscription_list_id = 'Y6tVdb'\n    const raw_payload = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': common_email_source,'profile': {'data': {'type': 'profile','attributes': {'email': registrationEmail}}}},'relationships': {'list': {'data': {'type': 'list','id': common_subscription_list_id}}}}});\n    const common_email_public_api = 'Ra4Ub5'\n    const requestHeaders = new Headers();requestHeaders.append('revision', '2024-02-15');requestHeaders.append('Content-Type', 'application/json');\n    const request_Options = {method: 'POST',headers: requestHeaders,body: raw_payload,redirect: 'follow'};\n    fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+common_email_public_api, request_Options).then((response) => response.text()).catch((error) => console.error('error',error));\n\tlocalStorage.removeItem('registration_form_submit');\n  }\nif(localStorage.getItem('sms_registration_form_submit') && window.location.href.indexOf('/challenge') == -1){\n  console.log(222)\n\tconst phone_number = localStorage.getItem('sms_registration_form_submit');\n\tconst common_sms_custom_source = 'Subscribe to SMS via Registration'\n\tconst common_sms_public_api = 'Ra4Ub5'\n\tconst common_sms_list_id = 'QQ98PR'\n\n\tconst myHeaders = new Headers();myHeaders.append('revision', '2024-02-15');myHeaders.append('Content-Type', 'application/json');\n\tconst raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': common_sms_custom_source,'profile': {'data': {'type': 'profile','attributes': {'phone_number': phone_number}}}},'relationships': {'list': {'data': {'type': 'list','id': common_sms_list_id}}}}});\n\tconst requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};\n\tfetch('https://a.klaviyo.com/client/subscriptions/?company_id='+common_sms_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));\n\tlocalStorage.removeItem('sms_registration_form_submit');\n  }\n});",
    "reamaze_lightbox": true,
    "reamaze_account": "melin",
    "reamaze_widget_color": "#000000",
    "reamaze_faces_image": "shopify://shop_images/melin-sq_small_8f9a22c6-e49c-429b-8e0d-8aa19fcffb65.png",
    "exponea": false,
    "exponea_cdt_api_url": "https://ai.melin.com",
    "exponea_token_id": "ce70f558-910f-11e9-a948-164746c4d9c4",
    "exponea_experiments_enabled": false,
    "exponea_experiments_mode": "async",
    "cookie_compliance_enabled": false,
    "cookie_compliance_id": "",
    "enable_accessibility_statement": true,
    "accessibility_statement_link": "shopify://pages/accessibility-statement",
    "accessibility_statement_text": "Accessibility Statement",
    "code_head_start": "<script>!function(){try{!function(t,i){if(!i.version){window.tatari=i,i.init=function(t,n){var e=function(t,n){i[n]=function(){t.push([n].concat(Array.prototype.slice.call(arguments,0)))}};\"track pageview identify\".split(\" \").forEach(function(t){e(i,t)}),i._i=t,i.config=n,i.pageview()},i.version=\"1.2.1\";var n=t.createElement(\"script\");n.type=\"text/javascript\",n.async=!0,n.src='https://d2hrivdxn8ekm8.cloudfront.net/tag-manager/a27630cb-251b-400e-9313-fa29620b60d1-latest.js';var e=t.getElementsByTagName(\"script\")[0];e.parentNode.insertBefore(n,e)}}(document,window.tatari||[])}catch(t){console.log(t)}}();tatari.init('a27630cb-251b-400e-9313-fa29620b60d1');</script>\n<!-- Edgemesh Client Begin -->\n<script type=\"application/javascript\">\n  EDGEMESH = {\n    lite: true\n  }\n</script>\n<script\n  async\n  type=\"application/javascript\"\n  src=\"https://static.edgeme.sh/client.js\">\n</script>\n<!-- Edgemesh Client End -->\n<!-- Start Intelligems -->\n<script>\n    window.Shopify = window.Shopify || {theme: {id: {{ theme.id }}, role: '{{ theme.role }}' } };\n    window._template = {\n        directory: \"{{ template.directory }}\",\n        name: \"{{ template.name }}\",\n        suffix: \"{{ template.suffix }}\"\n    };\n</script>\n<script type=\"module\" blocking=\"render\" fetchpriority=\"high\" src=\"https://cdn.intelligems.io/esm/0e78b2613992/bundle.js\" data-em-disable></script>\n<!-- End Intelligems -->",
    "code_head_end": "<!-- Google tag (gtag.js) -->\n{% if request.host contains \" melin.com \"%}\n <script async src=\"https://www.googletagmanager.com/gtag/js?id=AW-936877668\"></script>\n<script>\n window.dataLayer = window.dataLayer || [];\n function gtag(){dataLayer.push(arguments);}\n gtag('js', new Date());\n gtag('config', 'AW-936877668');\n</script> \n{% elsif request.host contains  \"melinbrand.ca\" %}\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=GT-KVHJB6MS\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n  gtag('config', 'GT-KVHJB6MS');\n</script>\n{% endif %}\n<!-- end Google tag (gtag.js) --> \n\n<!-- Google Search Console Verification for NO/JC/CA -->\n{% if request.host contains \"melin.com\" %}\n<meta name=\"google-site-verification\" content=\"PF8xOoUNJz-Fp6H5HYMQoBbgIcPSF-n_9wDqdR6RXQg\" />\n<meta name=\"google-site-verification\" content=\"iWh5vvuOKoqtiZw3L9rrNzWEfB3eOpy8yljeD1nKLio\" />{% elsif request.host contains \"melinbrand.ca\" %}\n <meta name=\"google-site-verification\" content=\"7PP31tZk6iywatYpXBtPIN0z93ZE82YtSBVL1nGG-eU\" />{% endif %}\n<!-- end Google Search -->\n<!-- Axon -->\n<script>\n  window.ALBSS = {\n    event_key: '343b5a41-3389-49ec-a8cf-d18af5cc2874'\n  };\n</script>\n<script src=\"https://c.albss.com/p/l/loader.iife.js\" async></script>\n<!-- Clarity -->\n<script type=\"text/javascript\">\n    (function(c,l,a,r,i,t,y){\n        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n        t=l.createElement(r);t.async=1;t.src=\"https://www.clarity.ms/tag/\"+i;\n        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n    })(window, document, \"clarity\", \"script\", \"koe1o09865\");\n</script>\n<!-- Clarity -->\n\n<script src=\"https://challenges.cloudflare.com/turnstile/v0/api.js\" async defer></script>",
    "code_body_end": "<!-- Sierra -->\n<script type=\"module\" src=\"https://sierra.chat/agent/DUBlA-4BkQD6HuoBkQD6aJguV4X1mMrVvmRwm6dN6OY/embed\"></script>\n<script>\n const params  = new URLSearchParams(window.location.search);\n const variable = params.get('variable');       // e.g. \"ra_id:1234\"\n /* ---------- 2. Derive locale from domain ---------- */\n const host  = window.location.hostname.toLowerCase(); // \"melinbrand.ca\"\n let locale = '';\n if (host.endsWith('.ca'))    locale = 'ca';\n else if (host.endsWith('.co.uk')) locale = 'uk';\n else if (host.endsWith('.com'))  locale = 'us';\n // Extend with more TLD rules as needed.\n var sierraConfig = {\n  variables: {\n   \"locale\": locale || \"\" , //if no match\n  },\nonDisabled() {\n    window.Reamaze.popup()\n            }\n };\n</script>\n<!-- /Sierra -->\n\n<script>\nwindow.addEventListener(\"klaviyoForms\", function(e) { \n  if (e.detail.type == 'submit') {\nfbq('track', 'Lead');\n}\n});\n</script>\n<!-- Bing -->\n<script>(function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:\"5933451\", enableAutoSpaTracking: true};o.q=w[u],w[u]=new UET(o),w[u].push(\"pageLoad\")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!==\"loaded\"&&s!==\"complete\"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)})(window,document,\"script\",\"//bat.bing.com/bat.js\",\"uetq\");</script>\n<!-- end Bing -->\n<!-- Start Postpilot -->\n<script type='text/javascript'>\n  var script = document.createElement('script');\n  script.src = 'https://xp2023-pix.s3.amazonaws.com/px_LUzV1.js';\n  document.getElementsByTagName('head')[0].appendChild(script);\n</script>\n<!-- End Postpilot -->\n{% if request.path == '/community/membership' %}\n  <meta name=\"robots\" content=\"noindex,nofollow\">\n{% endif %}",
    "split_content_for_header": "",
    "block_scripts": "",
    "script_block_exceptions": "",
    "script_cart_add": "",
    "melin_afterpay_enabled": true,
    "melin-pobox-blocker_enable": true,
    "melin_enable_gift_order": true,
    "melin_gift_svg": "https://cdn.shopify.com/s/files/1/1175/0278/files/Gift_Icon.svg?v=1669935490",
    "melin_free_shipping_amount": "99.00",
    "melin_address_validation_enable": true,
    "melin_estimated_shipping_delay": "1",
    "melin_cutoff_time": "17",
    "melin_blackout_dates": "11/26/20,12/24/20,12/25/20,1/1/21,11/26/21,12/25/21,1/1/22,5/30/22,11/24/22, 11/27/22,12/25/22,1/1/23,1/5/23,1/20/23,5/29/23,6/19/23,7/4/23,10/9/23,12/25/23,1/1/24,1/15/24",
    "melin_shipping_text_message": "We do not ship to P.O. Boxes. Selecting an expedited shipping method only expedites the transit time. The estimated arrival dates include processing and transit times. When your order ships, you will receive a tracking number to follow your shipment from our distribution center to your front door.",
    "melin_estimated_text_space": "53",
    "melin_shipping_text_loading_message": "Est. Arrival: Checking with the locals. BRB",
    "melin_shipping_map": "{\n  \"shipping_map\": [\n    {\n      \"carrier\": \"ups\",\n      \"shipping_methods\": []\n    },\n    {\n      \"carrier\": \"fedex\",\n      \"shipping_methods\": [\n        {\n          \"shopify_title\": \"Standard\",\n          \"carrier_code\": \"FEDEX_GROUND\",\n          \"delay\": 3,\n          \"delay_end\": 3,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"melin Family Free Shipping\",\n          \"carrier_code\": \"FEDEX_GROUND\",\n          \"delay\": 1,\n          \"delay_end\": 3,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"FedEx 2Day®\",\n          \"carrier_code\": \"FEDEX_2_DAY\",\n          \"delay\": 0,\n          \"delay_end\": 1,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"melin Family Expedited Shipping\",\n          \"carrier_code\": \"FEDEX_2_DAY\",\n          \"delay\": 0,\n          \"delay_end\": 1,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"FedEx Standard Overnight®\",\n          \"carrier_code\": \"PRIORITY_OVERNIGHT\",\n          \"delay\": 1,\n          \"delay_end\": null,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"Outsider Members Free Shipping\",\n          \"carrier_code\": \"FEDEX_GROUND\",\n          \"delay\": 0,\n          \"delay_end\": 1,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"Outsider Members Hawaii Shipping\",\n          \"carrier_code\": \"FEDEX_GROUND\",\n          \"delay\": 0,\n          \"delay_end\": 1,\n          \"date_show\": null,\n          \"date_hide\": null\n        }\n      ]\n    },\n    {\n      \"carrier\": \"other\",\n      \"shipping_methods\": [\n        {\n          \"shopify_title\": \"Economy\",\n          \"delay\": 4,\n          \"delay_end\": 2,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"International Economy\",\n          \"delay\": 10,\n          \"delay_end\": 5,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"International Express\",\n          \"delay\": 4,\n          \"delay_end\": 2,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"Priority Mail\",\n          \"delay\": 6,\n          \"delay_end\": 2,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"melin Family US Territories Shipping\",\n          \"delay\": 6,\n          \"delay_end\": 2,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"Irvine HQ\",\n          \"delay\": 3,\n          \"delay_end\": null,\n          \"date_show\": null,\n          \"date_hide\": null\n        },\n        {\n          \"shopify_title\": \"Christmas Delivery\",\n          \"delay\": null,\n          \"delay_end\": null,\n          \"date_show\": \"2023/12/24\",\n          \"date_hide\": \"2023/12/20 8:59\"\n        },\n        {\n          \"shopify_title\": \"melin Members Free Shipping\",\n          \"delay\": 6,\n          \"delay_end\": 2,\n          \"date_show\": null,\n          \"date_hide\": null\n        }\n      ]\n    }\n  ]\n}",
    "melin_gtm_container_id": "GTM-5NTHZ85",
    "type_header_font": "sans-serif",
    "type_subheader_font": "sans-serif",
    "type_body_font": "sans-serif",
    "type_nav_font": "sans-serif",
    "sections": {
      "header": {
        "type": "header",
        "blocks": {
          "17f75e6b-2631-4f03-9081-d38c962805c5": {
            "type": "bar",
            "settings": {
              "title": "⤷ Bar",
              "bar_class_role": "header-bar--main-promo",
              "bar_class_position": "sticky top-0",
              "bar_style_background_color": "#000000",
              "bar_style_color": "#ffffff",
              "transparency_paths": "",
              "bar_transparent_text_color": ""
            }
          },
          "852a9e33-7fb9-4e3c-9cbb-c4daa3d39d5f": {
            "type": "announcements",
            "settings": {
              "title": "⤷ Announcements",
              "column_class_width": "w-full",
              "column_class_width_desktop": "lg:w-full",
              "column_classes": "text-center",
              "interval": 3
            }
          },
          "announcement_TtmzCQ": {
            "type": "announcement",
            "settings": {
              "inclusion": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "text": "Free Shipping With Purchase Of 2+ Items",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://support.melin.com/en-US/free-shipping-on-all-orders-dollar120+-1118813"
            }
          },
          "announcement_8YKVEF": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "text": "Free Shipping On All Orders $120+",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://support.melin.com/en-US/free-shipping-on-all-orders-dollar120+-1118813"
            }
          },
          "announcement_pT3FMg": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "text": "Free Shipping on orders 150+",
              "text_desktop": "Free Shipping on orders 150+",
              "svg": "",
              "image_size": "",
              "link": ""
            }
          },
          "dc8f77a1-ce6f-418e-a356-b96d64c1c88c": {
            "type": "announcement",
            "settings": {
              "inclusion": "1",
              "inclusion_js": "",
              "text": "Free & Easy Exchanges",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://support.melin.com/en-US/what-is-your-return-policy-d8f986205b5069d9-1118845"
            }
          },
          "7e1136c2-b890-4fbc-b207-b2e4b8b43e93": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "text": "Sign Up To Win A Free Hat!",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://melin-brand.myshopify.com/pages/hat-a-day-giveaway"
            }
          },
          "2a2f7a02-f7c2-4d5c-b1cf-dd4c7a470b1d": {
            "type": "bar",
            "settings": {
              "title": "⤷ Bar",
              "bar_class_role": "header-bar--main",
              "bar_class_position": "sticky top-0",
              "bar_style_background_color": "",
              "bar_style_color": "",
              "transparency_paths": "",
              "bar_transparent_text_color": ""
            }
          },
          "766c9413-2966-44a4-b600-b7f4c58e5cff": {
            "type": "menu-toggle",
            "settings": {
              "column_classes": "lg:hidden",
              "onclick": "document.querySelector('.header-bar--main').classList.toggle('active');\ndocument.querySelector('.header-bar--main details').open=true;\ndocument.querySelector('.header-bar--main details').click();"
            }
          },
          "dc210b9c-d0d2-402f-98dc-ec7e0178e9fe": {
            "type": "logo",
            "settings": {
              "column_classes": "justify-start",
              "logo": "<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"155px\" height=\"19px\" viewBox=\"0 0 155 19\" style=\"enable-background:new 0 0 155 19;\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xml:space=\"preserve\"  role=\"img\" \n  aria-labelledby=\"svgTitle\">\n  <title id=\"svgTitle\">melin logo</title>\n<g>\n\t<path d=\"M0.202,0.071v18.858h17.02h17.02V0.071l-17.02,8.422L0.202,0.071z M9.13,16.693H3.492V5.352L9.13,8.047V16.693z\n\t\t M16.49,16.693h-5.914V8.738l5.914,2.827V16.693z M25.314,8.047l5.639-2.695v11.341h-5.639V8.047z M17.955,11.565l5.914-2.827\n\t\tv7.956h-5.914V11.565z\"></path>\n\t<path d=\"M119.509,6.447h0.002c1.188-0.003,2.161-0.922,2.166-2.053c-0.005-1.125-0.978-2.043-2.168-2.046\n\t\tc-1.19,0.003-2.162,0.921-2.168,2.051C117.346,5.526,118.319,6.445,119.509,6.447z\"></path>\n\t<path d=\"M146.626,4.539c-2.368-1.318-5.248-1.986-8.559-1.986c-3.292,0-6.176,0.676-8.572,2.009\n\t\tc-2.528,1.398-3.81,3.163-3.81,5.245v9.051h4.385v-9.05c0.002-1.049,0.686-1.921,2.152-2.744c1.605-0.897,3.572-1.351,5.845-1.351\n\t\tc2.263,0,4.212,0.454,5.796,1.351c1.488,0.825,2.183,1.698,2.187,2.744v9.051h4.383v-9.05\n\t\tC150.438,7.714,149.157,5.94,146.626,4.539z\"></path>\n\t<path d=\"M109.811,15.518c-0.148-0.089-0.148-0.098-0.154-0.23V2.372h-4.343v12.916c0,0.417,0.027,0.968,0.23,1.521\n\t\tc0.226,0.654,0.658,1.174,1.253,1.505c0.927,0.503,2.045,0.547,3.206,0.549h0.001c0.178,0,0.356-0.001,0.531-0.002l3.097-0.002\n\t\tv-3.141h-2.574C110.226,15.717,109.886,15.56,109.811,15.518z\"></path>\n\t<rect x=\"117.323\" y=\"9.034\" width=\"4.38\" height=\"9.812\"></rect>\n\t<path d=\"M97.848,4.536c-2.368-1.316-5.235-1.983-8.52-1.983c-3.291,0-6.174,0.676-8.568,2.009c-2.492,1.375-3.806,3.19-3.802,5.246\n\t\tv2.047c-0.005,2.096,1.274,3.868,3.819,5.278c2.384,1.192,5.26,1.797,8.551,1.797c3.411,0,6.359-0.652,8.76-1.937l1.316-0.703\n\t\tl-3.657-2.028l-0.389,0.24c-1.661,0.976-3.69,1.471-6.03,1.471c-2.224,0-4.198-0.448-5.855-1.326\n\t\tc-1.219-0.687-1.902-1.406-2.108-2.233h20.33V9.808C101.698,7.696,100.402,5.922,97.848,4.536z M81.368,9.253\n\t\tc0.209-0.808,0.891-1.513,2.107-2.189c1.611-0.897,3.581-1.352,5.853-1.352c2.268,0,4.217,0.454,5.798,1.351\n\t\tc1.204,0.665,1.901,1.385,2.116,2.189H81.368z\"></path>\n\t<path d=\"M70.553,4.129c-1.84-1.046-4.071-1.576-6.629-1.576c-1.653,0-3.186,0.222-4.554,0.659c-1.131,0.354-2.1,0.822-2.885,1.394\n\t\tc-0.802-0.571-1.772-1.039-2.888-1.393c-1.362-0.438-2.891-0.66-4.543-0.66c-2.617,0-4.851,0.532-6.638,1.582\n\t\tc-1.968,1.145-2.966,2.55-2.966,4.178v10.546h4.342l0-10.586L43.79,8.188c-0.002-0.551,0.43-1.063,1.319-1.563\n\t\tc1.086-0.605,2.414-0.912,3.945-0.912c1.516,0,2.829,0.306,3.905,0.911c1.224,0.689,1.365,1.294,1.365,1.689v10.546h4.334V8.248\n\t\tc0.035-0.398,0.226-0.995,1.405-1.643c1.056-0.591,2.355-0.892,3.861-0.892c1.498,0,2.784,0.298,3.826,0.89\n\t\tc1.181,0.649,1.366,1.245,1.397,1.641v10.615h4.381V8.247C73.53,6.629,72.5,5.204,70.553,4.129z\"></path>\n\t<polygon points=\"149.989,3.26 150.704,3.26 150.704,5.351 151.3,5.351 151.3,3.26 152.018,3.26 152.018,2.712 149.989,2.712 \t\"></polygon>\n\t<path d=\"M154.634,2.712h-0.625l-0.454,1.233c-0.043,0.121-0.08,0.23-0.113,0.331c-0.031-0.101-0.068-0.21-0.108-0.331L152.9,2.712\n\t\th-0.625l-0.184,2.639h0.581l0.071-1.144c0.004-0.059,0.007-0.12,0.01-0.181c0.01,0.03,0.021,0.062,0.031,0.094l0.414,1.217h0.435\n\t\tl0.45-1.234c0.016-0.046,0.033-0.091,0.048-0.135c0.004,0.074,0.008,0.146,0.011,0.215l0.068,1.168h0.588L154.634,2.712z\"></path>\n</g>\n</svg>"
            }
          },
          "09869aa9-b2d7-43ec-963b-34b909a325a6": {
            "type": "spacer",
            "settings": {
              "column_classes": "ml-auto"
            }
          },
          "9770ae93-2b88-40da-ad55-45003629b88b": {
            "type": "menu",
            "settings": {
              "column_classes": "hidden group-active:flex lg:flex justify-center",
              "classes_format": "flex-row",
              "menu": "main-menu-arch",
              "hide_mobile": "explore",
              "hide_desktop": "",
              "mega_menu_hover": true
            }
          },
          "8d614d9d-3bb9-4c43-b3f6-f1caa10d1df1": {
            "type": "spacer",
            "settings": {
              "column_classes": "ml-auto"
            }
          },
          "8a34200f-ab57-4d1a-b680-b1356e3dca84": {
            "type": "search",
            "settings": {
              "column_classes": "order-3 lg:order-1 w-full lg:w-auto lg:mr-6",
              "input_focus": "",
              "search_suggestions": "{% unless cart.currency.iso_code == 'CAD' %}\"realtree\", \n{% endunless %}\"hydro\", \"golf\", \"odysea\", \"camo\", \"XL\", \"coronado\", \"hydrolite\", \"dad hat\", \"adventure club\"",
              "search_terms_animation_speed": 2,
              "search_terms_inbetween_distance": 20,
              "search_terms_position": 80,
              "search_terms_position_tablet": 90,
              "search_terms_position_mobile": 90,
              "search_terms_classes": "text-[16px] lg:text-[12px] text-inherit",
              "search_terms_color": "#000000",
              "search_terms_color_opacity": 50,
              "search_terms_animation_delay": 1000
            }
          },
          "6d98e9ce-6f74-4acf-8881-e952769aa5c5": {
            "type": "nav-tools",
            "settings": {
              "column_classes": "order-2 gap-sm",
              "button_classes": "",
              "search": false,
              "wishlist": false,
              "account": true,
              "geo": false,
              "cart": true,
              "icon_size": 24,
              "display": "icons",
              "greeting": ""
            }
          },
          "a44f9598-3a1e-472e-8b0f-bb2e0de9f9c3": {
            "type": "menu",
            "settings": {
              "column_classes": "hidden group-active:block",
              "classes_format": "flex-col",
              "menu": "new-theme-explore-nav",
              "hide_mobile": "",
              "hide_desktop": "",
              "mega_menu_hover": false
            }
          }
        },
        "block_order": [
          "17f75e6b-2631-4f03-9081-d38c962805c5",
          "852a9e33-7fb9-4e3c-9cbb-c4daa3d39d5f",
          "announcement_TtmzCQ",
          "announcement_8YKVEF",
          "announcement_pT3FMg",
          "dc8f77a1-ce6f-418e-a356-b96d64c1c88c",
          "7e1136c2-b890-4fbc-b207-b2e4b8b43e93",
          "2a2f7a02-f7c2-4d5c-b1cf-dd4c7a470b1d",
          "766c9413-2966-44a4-b600-b7f4c58e5cff",
          "dc210b9c-d0d2-402f-98dc-ec7e0178e9fe",
          "09869aa9-b2d7-43ec-963b-34b909a325a6",
          "9770ae93-2b88-40da-ad55-45003629b88b",
          "8d614d9d-3bb9-4c43-b3f6-f1caa10d1df1",
          "8a34200f-ab57-4d1a-b680-b1356e3dca84",
          "6d98e9ce-6f74-4acf-8881-e952769aa5c5",
          "a44f9598-3a1e-472e-8b0f-bb2e0de9f9c3"
        ],
        "settings": {
          "unwrap": true
        }
      },
      "footer": {
        "type": "footer",
        "blocks": {
          "43b46537-7741-484f-ab80-9097f4604966": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap"
            }
          },
          "44c27b5a-a24e-44b2-b593-b113f1d4d85d": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-auto",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-3xl"
            }
          },
          "frame_CwTpRq": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "w-auto",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "07297159-1035-4053-a76a-4ce60dbc70a5": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "footer-get-melin",
              "columns": 2,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "break_P83ged": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "frame_4GiddA": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "w-auto",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "menu_item_eN7akV": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "footer-get-melin-ca",
              "columns": 2,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "break_KWemAp": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "menu_item_tFDdtB": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "footer-customer-service",
              "columns": 3,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "0fee3780-e75b-4ae7-9fd0-324bfa80d48b": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "footer-our-company",
              "columns": 2,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "e76cdf5e-2f09-4126-89b7-61aeec827d45": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap"
            }
          },
          "da563e74-44b7-4187-b39b-f9affa88e360": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-2xl"
            }
          }
        },
        "block_order": [
          "43b46537-7741-484f-ab80-9097f4604966",
          "44c27b5a-a24e-44b2-b593-b113f1d4d85d",
          "frame_CwTpRq",
          "07297159-1035-4053-a76a-4ce60dbc70a5",
          "break_P83ged",
          "frame_4GiddA",
          "menu_item_eN7akV",
          "break_KWemAp",
          "menu_item_tFDdtB",
          "0fee3780-e75b-4ae7-9fd0-324bfa80d48b",
          "e76cdf5e-2f09-4126-89b7-61aeec827d45",
          "da563e74-44b7-4187-b39b-f9affa88e360"
        ],
        "custom_css": [
          ".footer__credits {margin-top: 1.2rem; padding: 1.2rem 1rem; align-items: flex-end; border-top: #000000;}",
          ".menu-item ul li > ul > li a {padding-right: 0.725rem;}",
          ".footer__credits {flex-direction: row; padding-top: 1.5rem;}"
        ],
        "settings": {
          "wrapper_style_background_color": "#000000",
          "wrapper_style_background": "",
          "wrapper_class_vertical_padding": "",
          "wrapper_class_horizontal_padding": "",
          "wrapper_class_vertical_padding_desktop": "lg:py-xl",
          "wrapper_class_horizontal_padding_desktop": "lg:px-4xl",
          "container_class_container": "w-full",
          "container_class_vertical_padding": "py-0",
          "container_class_horizontal_padding": "px-0",
          "container_class_vertical_padding_desktop": "lg:py-0",
          "container_class_horizontal_padding_desktop": "lg:px-0",
          "credits": "<span>© 2025 melin. All Rights Reserved.</span>",
          "credits_link_list": "terms-menu"
        }
      },
      "mega-menu": {
        "type": "mega-menu",
        "blocks": {
          "d82fa995-f888-4e67-83dd-b4fdd4d7cbd4": {
            "type": "menu",
            "settings": {
              "title": "Featured",
              "teleport": "#mega-featured",
              "redirect": "",
              "wrapper_style_background_color": "#ededed",
              "container_class_container": "container",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "2acf5995-d6c2-4aef-b867-91b91165042d": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "rgba(0,0,0,0)",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "lg:py-2xl",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-2xl",
              "article_class_custom": ""
            }
          },
          "420a7d90-329b-4f07-a682-ce4486cfe710": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-3/5",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-2",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "",
              "article_class_horizontal_padding_desktop": "lg:px-4xl",
              "article_class_gap_desktop": "lg:gap-lg",
              "article_class_custom": ""
            }
          },
          "67e7cf4d-4853-4fcb-b27d-490c6156ed6e": {
            "type": "content-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "item_class_visibility": "max-lg:hidden",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "",
              "content_class_horizontal_padding_desktop": "",
              "content_class_gap_desktop": "",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-center layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "",
              "text_stack_class_horizontal_padding_desktop": "",
              "text_stack_class_gap_desktop": "",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-center layout-middle",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "",
              "buttons__horizontal_padding_desktop": "",
              "buttons__gap_desktop": "",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "frame_kPYpVw": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "nav_item_CqCQQM": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://products/melin-e-gift-card",
              "title": "",
              "description": ""
            }
          },
          "break_yTfW9J": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "nav_item_7Yq3iU": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://pages/fathers-day-gifts",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_24.png",
              "title": "Father's Day Gifts",
              "description": ""
            }
          },
          "nav_item_a4g9dA": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "https://melin.com/products/odysea-ohana-hydro-black?view=melin-x-olukai&variant=42027472715872",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_-_1_493b199e-380b-4f3c-b09d-b89532c8a4ab.png",
              "title": "melin x OluKai",
              "description": ""
            }
          },
          "nav_item_AmwApg": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/new-arrivals",
              "image": "shopify://shop_images/70478_SLIM_001_2.png",
              "title": "New Arrivals",
              "description": ""
            }
          },
          "nav_item_KyAzpn": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/new-arrivals-canada",
              "image": "shopify://shop_images/70408_BLK_001.webp",
              "title": "New Arrivals",
              "description": ""
            }
          },
          "539f00e4-518b-4dde-bccd-80bb3ec596b0": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/most-popular",
              "image": "shopify://shop_images/70131_BLK_001.png",
              "title": "Most Popular",
              "description": ""
            }
          },
          "nav_item_DRfcTE": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/most-popular-canada",
              "image": "shopify://shop_images/70131_BLK_001.png",
              "title": "Most Popular",
              "description": ""
            }
          },
          "nav_item_bXLPAj": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/signature-collaborations",
              "image": "shopify://shop_images/70212_RWBL_001_2.png",
              "title": "Signature Collabs",
              "description": ""
            }
          },
          "758b759d-3240-4fc9-8ce2-ec3a90139eae": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/accessories",
              "image": "shopify://shop_images/70182_BLK_001_528e8ba4-cba9-46bb-9acc-ad6a21ca36e0.png",
              "title": "Accessories",
              "description": ""
            }
          },
          "a3c3d16c-474c-4bed-8ecf-735f9ee8b28f": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://products/melin-e-gift-card",
              "image": "shopify://shop_images/Nav-Icon.png",
              "title": "Gift Cards",
              "description": ""
            }
          },
          "nav_item_gJJ6tM": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://products/melin-e-gift-card",
              "title": "",
              "description": ""
            }
          },
          "29e8bb15-b40a-4649-be65-c03336869457": {
            "type": "content-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "item_class_visibility": "max-lg:hidden",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "",
              "content_class_horizontal_padding_desktop": "",
              "content_class_gap_desktop": "",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-center layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "",
              "text_stack_class_horizontal_padding_desktop": "",
              "text_stack_class_gap_desktop": "",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-center layout-middle",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "",
              "buttons__horizontal_padding_desktop": "",
              "buttons__gap_desktop": "",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "e2dfb46f-ebd5-46ab-8fbc-4b125f0f0d1c": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "cce30427-d7dc-4e2a-aa8b-b318cd3ffdaa": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "",
              "content_class_horizontal_padding_desktop": "",
              "content_class_gap_desktop": "",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-center layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "",
              "text_stack_class_horizontal_padding_desktop": "",
              "text_stack_class_gap_desktop": "",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-center layout-middle",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "",
              "buttons__horizontal_padding_desktop": "",
              "buttons__gap_desktop": "",
              "button_1_text": "Shop Featured",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/featured",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "10f68e44-88ac-412f-9e90-0e4b860b37b5": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "71e690f2-b86f-4188-8c76-d5a53b8a8ddd": {
            "type": "menu",
            "settings": {
              "title": "Shop",
              "teleport": "#mega-shop",
              "redirect": "",
              "wrapper_style_background_color": "#ededed",
              "container_class_container": "container",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "46da8383-703a-469b-8184-db5406e94502": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "rgba(0,0,0,0)",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-2xl",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-2xl",
              "article_class_custom": ""
            }
          },
          "c1621cc7-e78e-41a4-99b7-502075d3bdb6": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-4/5",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-2",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-lg",
              "article_class_custom": ""
            }
          },
          "7ebb9e82-7ce9-46b9-9018-dbae0f53112a": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/a-game",
              "image": "shopify://shop_images/70131_BLK_001.png",
              "title": "A-Game",
              "description": ""
            }
          },
          "b9e1de59-633a-4815-9584-bde24977f7ae": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/odysea",
              "image": "shopify://shop_images/70240_NVY_001.png",
              "title": "Odysea",
              "description": ""
            }
          },
          "173aac9f-ffac-483c-ac08-6180bad7dc1a": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/trenches",
              "image": "shopify://shop_images/70149_MAR_001.png",
              "title": "Trenches",
              "description": ""
            }
          },
          "21be92e2-5387-4486-a89c-72f319ee42d2": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/coronado",
              "image": "shopify://shop_images/70275_HTCH_001.png",
              "title": "Coronado",
              "description": ""
            }
          },
          "nav_item_9kAU7F": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/the-legend",
              "image": "shopify://shop_images/70350_DKKH_001_b3e01d75-0fbd-4fb1-8e62-98073b364d82.png",
              "title": "Legend",
              "description": ""
            }
          },
          "nav_item_MEFGpf": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://products/the-shore-islands-hydro-hawaii-camo",
              "image": "shopify://shop_images/70426_HICA_001_2.png",
              "title": "Shore",
              "description": ""
            }
          },
          "7ab8c839-c8d2-42e1-b202-be97232ecbf2": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://products/pace-hydro-waterproof-strapback-hat-black",
              "image": "shopify://shop_images/70210_BLK_001.png",
              "title": "Pace",
              "description": ""
            }
          },
          "nav_item_8cBYX9": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/caddy",
              "image": "shopify://shop_images/70451_SUAU_001_3aa62510-f292-4fb9-b9a7-9a941cee3bf2.png",
              "title": "Caddy",
              "description": ""
            }
          },
          "nav_item_UjfimW": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/coast",
              "image": "shopify://shop_images/70341_NEAQ_001.png",
              "title": "Coast",
              "description": ""
            }
          },
          "25faef33-c50f-491f-bebf-019ae19876bc": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/beanies",
              "image": "shopify://shop_images/100000098_TOBA_001_218220a3-**************-163713b61bc7.png",
              "title": "Beanies",
              "description": ""
            }
          },
          "a984d157-73c1-4939-9aeb-3623ecb72801": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_KHVBxr": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "container",
              "article_class_width_desktop": "lg:container",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "b8c0d62c-b610-4669-9784-1fe286a311a3": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-lg",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "Shop All",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/shop-all",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "break_e688eX": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_8tXX4Y": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "container",
              "article_class_width_desktop": "lg:container",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "content_item_KhUyB3": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-lg",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "Shop All",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/shop-all-hats",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "break_X4aAKn": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "61a23c44-0885-4684-ab73-7f7dbc05c80d": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "fbc7dfa4-8460-41d9-9b16-e37cf7d0bebe": {
            "type": "menu",
            "settings": {
              "title": "Collections",
              "teleport": "#mega-collections",
              "redirect": "",
              "wrapper_style_background_color": "#ededed",
              "container_class_container": "container",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "892551b0-c34f-4785-9823-6467c7ee0296": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "lg:py-2xl",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-2xl",
              "article_class_custom": ""
            }
          },
          "f60c3456-5977-41fc-a6f3-54428ebe8b90": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-9/10",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-2",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-lg",
              "article_class_custom": ""
            }
          },
          "content_item_JmrLG9": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "item_class_visibility": "max-lg:hidden",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "428fd94e-e859-4593-8583-364ab8824eb8": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/hydro",
              "image": "shopify://shop_images/Collections---Hydro_2x_1.png",
              "title": "Hydro",
              "description": ""
            }
          },
          "nav_item_PTmUCd": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/hydro-collection",
              "image": "shopify://shop_images/Collections---Hydro_2x_1.png",
              "title": "Hydro",
              "description": ""
            }
          },
          "40f4bd57-4884-4bd2-812a-5ea253b715d5": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/thermal",
              "image": "shopify://shop_images/Collections---Thermal_2x_1.png",
              "title": "Thermal",
              "description": ""
            }
          },
          "nav_item_Tk8FRz": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/thermal-collection",
              "image": "shopify://shop_images/Collections---Thermal_2x_1.png",
              "title": "Thermal",
              "description": ""
            }
          },
          "nav_item_JxKgnb": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes(\"melinbrand.ca\")",
              "link": "shopify://collections/adventure-club",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_3bcb120c-1d4c-4b03-ac58-260df4aab2ce.png",
              "title": "Adventure Club",
              "description": ""
            }
          },
          "nav_item_W3Nk8j": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "",
              "inclusion_js": "!window.location.hostname.includes(\"melinbrand.ca\")",
              "link": "shopify://collections/adventure-club",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_3bcb120c-1d4c-4b03-ac58-260df4aab2ce.png",
              "title": "Adventure Club",
              "description": ""
            }
          },
          "nav_item_4xHDfK": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/hydrolite",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_1_07ca9f7e-35fb-4399-9bce-70e5f68d48ab.png",
              "title": "Hydrolite",
              "description": ""
            }
          },
          "nav_item_iGUGL7": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/hydrolite",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_1_07ca9f7e-35fb-4399-9bce-70e5f68d48ab.png",
              "title": "Hydrolite",
              "description": ""
            }
          },
          "06cfbca4-3f27-4070-ae6d-1e0f2b2d81d3": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/links-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500-2.png",
              "title": "Links",
              "description": ""
            }
          },
          "nav_item_WdEafC": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/sandy-shores-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_Black_-_500x500_66dbb3ff-d893-4ef0-9b64-e39287674941.png",
              "title": "Sandy Shores",
              "description": ""
            }
          },
          "nav_item_dJqYG3": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "https://melin.com/products/odysea-ohana-hydro-black?view=melin-x-olukai&variant=42027472715872",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_-_1.png",
              "title": "melin x OluKai",
              "description": ""
            }
          },
          "nav_item_XBwnrh": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/aura-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_27.png",
              "title": "Aura",
              "description": ""
            }
          },
          "nav_item_ct7Fd3": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/neon-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_25.png",
              "title": "Neon Splatter",
              "description": ""
            }
          },
          "nav_item_qDcmep": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/americana-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_1_1_29eee0e3-fc8a-4e98-96ff-b674e48f3208.png",
              "title": "Americana",
              "description": ""
            }
          },
          "nav_item_9LAFYi": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/summer-daze-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_1_1357c90e-8364-4705-a535-8d7c3c15f16c.png",
              "title": "Summer Daze",
              "description": ""
            }
          },
          "nav_item_YVXXp4": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/melin-x-realtree",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_19.png",
              "title": "melin x Realtree",
              "description": ""
            }
          },
          "nav_item_8zYyKp": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/melin-x-realtree",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_19.png",
              "title": "melin x Realtree",
              "description": ""
            }
          },
          "nav_item_GdtyUb": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/islands-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_20.png",
              "title": "Islands",
              "description": ""
            }
          },
          "nav_item_GzayEY": {
            "type": "nav-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/links-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500-2.png",
              "title": "Links",
              "description": ""
            }
          },
          "nav_item_DGBUnN": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/geopop-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_11.png",
              "title": "GeoPop",
              "description": ""
            }
          },
          "nav_item_ngg6Dm": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "link": "shopify://collections/black-white-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_21.png",
              "title": "Black / White",
              "description": ""
            }
          },
          "nav_item_8J4UkU": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "",
              "inclusion_js": "",
              "link": "shopify://collections/kombu-green",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_5.png",
              "title": "Kombu Green",
              "description": ""
            }
          },
          "nav_item_U47YNU": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "",
              "inclusion_js": "",
              "link": "shopify://collections/deep-dive-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_6.png",
              "title": "Deep Dive",
              "description": ""
            }
          },
          "nav_item_XTQ4Dg": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/aqua-splash",
              "image": "shopify://shop_images/Collection_Menu_Header_2_-_500x500_6ab211ea-8c3d-4561-a686-a795a8ccb927.png",
              "title": "Aqua Splash",
              "description": ""
            }
          },
          "nav_item_kQ98MV": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/mph-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_7.png",
              "title": "MPH",
              "description": ""
            }
          },
          "nav_item_HDTPE6": {
            "type": "nav-item",
            "disabled": true,
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "link": "shopify://collections/americana-collection",
              "image": "shopify://shop_images/Collection_Menu_Header_-_500x500_1_3.png",
              "title": "Americana",
              "description": ""
            }
          },
          "content_item_pE3fYQ": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-1/5",
              "item_class_visibility": "max-lg:hidden",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "adb63f00-9f5b-46ce-8474-ab08ac6b646d": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "455693c9-1a68-4851-afe8-dfe7a4b27b41": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "absolute inset-0 h-full w-full",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-middle",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "",
              "content_class_horizontal_padding_desktop": "",
              "content_class_gap_desktop": "",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-center layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "",
              "text_stack_class_horizontal_padding_desktop": "",
              "text_stack_class_gap_desktop": "",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-center layout-middle",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "",
              "buttons__horizontal_padding_desktop": "",
              "buttons__gap_desktop": "",
              "button_1_text": "Shop Collections",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/shop-all-collections",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--light",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "8cb307a3-b1a2-4729-b5d6-f83a195639a2": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "09e48b1e-f439-402d-87e0-1e55465f13a0": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "c59a01c5-0b2b-440e-859d-480a52de5f59": {
            "type": "menu",
            "settings": {
              "title": "Explore",
              "teleport": "#mega-explore",
              "redirect": "",
              "wrapper_style_background_color": "#ededed",
              "container_class_container": "w-full",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "306f11a6-e074-4e04-8bc2-004697082c15": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "rgba(0,0,0,0)",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "a91be5be-30f1-4964-aba3-7915356419c1": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-2",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "a1574118-b48e-4862-aa4b-e64d155f5b3e": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "image": "shopify://shop_images/Explore_-_Company_2x_b35836c0-002e-408c-8861-1fbac2264776.jpg",
              "link_list": "mega-sub-our-company",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "px-xl",
              "item_class_gap": "gap-lg",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "lg:px-3xl",
              "item_class_gap_desktop": "lg:gap-lg",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0",
              "default_open_mobile": ""
            }
          },
          "fcd6b399-774b-4a02-b4b8-7e981ba7ffcb": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "image": "shopify://shop_images/Explore_-_Tech_2x_c4f2180c-8c0c-4e1c-9fa7-b510c2813f33.jpg",
              "link_list": "mega-sub-nav-our-product",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "px-xl",
              "item_class_gap": "gap-lg",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "lg:px-3xl",
              "item_class_gap_desktop": "lg:gap-lg",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0",
              "default_open_mobile": ""
            }
          },
          "406632ee-8b72-4d9d-9437-45f5c6f9e173": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "image": "shopify://shop_images/Explore_-_Family_2x_ce2c350a-b49d-4bca-8d4f-f8596c540d46.jpg",
              "link_list": "mega-sub-nav-our-family",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "px-xl",
              "item_class_gap": "gap-lg",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "lg:px-3xl",
              "item_class_gap_desktop": "lg:gap-lg",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0",
              "default_open_mobile": ""
            }
          },
          "c03557f1-e54f-4fe2-8140-e0e17f9e2ad7": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          }
        },
        "block_order": [
          "d82fa995-f888-4e67-83dd-b4fdd4d7cbd4",
          "2acf5995-d6c2-4aef-b867-91b91165042d",
          "420a7d90-329b-4f07-a682-ce4486cfe710",
          "67e7cf4d-4853-4fcb-b27d-490c6156ed6e",
          "frame_kPYpVw",
          "nav_item_CqCQQM",
          "break_yTfW9J",
          "nav_item_7Yq3iU",
          "nav_item_a4g9dA",
          "nav_item_AmwApg",
          "nav_item_KyAzpn",
          "539f00e4-518b-4dde-bccd-80bb3ec596b0",
          "nav_item_DRfcTE",
          "nav_item_bXLPAj",
          "758b759d-3240-4fc9-8ce2-ec3a90139eae",
          "a3c3d16c-474c-4bed-8ecf-735f9ee8b28f",
          "nav_item_gJJ6tM",
          "29e8bb15-b40a-4649-be65-c03336869457",
          "e2dfb46f-ebd5-46ab-8fbc-4b125f0f0d1c",
          "cce30427-d7dc-4e2a-aa8b-b318cd3ffdaa",
          "10f68e44-88ac-412f-9e90-0e4b860b37b5",
          "71e690f2-b86f-4188-8c76-d5a53b8a8ddd",
          "46da8383-703a-469b-8184-db5406e94502",
          "c1621cc7-e78e-41a4-99b7-502075d3bdb6",
          "7ebb9e82-7ce9-46b9-9018-dbae0f53112a",
          "b9e1de59-633a-4815-9584-bde24977f7ae",
          "173aac9f-ffac-483c-ac08-6180bad7dc1a",
          "21be92e2-5387-4486-a89c-72f319ee42d2",
          "nav_item_9kAU7F",
          "nav_item_MEFGpf",
          "7ab8c839-c8d2-42e1-b202-be97232ecbf2",
          "nav_item_8cBYX9",
          "nav_item_UjfimW",
          "25faef33-c50f-491f-bebf-019ae19876bc",
          "a984d157-73c1-4939-9aeb-3623ecb72801",
          "frame_KHVBxr",
          "b8c0d62c-b610-4669-9784-1fe286a311a3",
          "break_e688eX",
          "frame_8tXX4Y",
          "content_item_KhUyB3",
          "break_X4aAKn",
          "61a23c44-0885-4684-ab73-7f7dbc05c80d",
          "fbc7dfa4-8460-41d9-9b16-e37cf7d0bebe",
          "892551b0-c34f-4785-9823-6467c7ee0296",
          "f60c3456-5977-41fc-a6f3-54428ebe8b90",
          "content_item_JmrLG9",
          "428fd94e-e859-4593-8583-364ab8824eb8",
          "nav_item_PTmUCd",
          "40f4bd57-4884-4bd2-812a-5ea253b715d5",
          "nav_item_Tk8FRz",
          "nav_item_JxKgnb",
          "nav_item_W3Nk8j",
          "nav_item_4xHDfK",
          "nav_item_iGUGL7",
          "06cfbca4-3f27-4070-ae6d-1e0f2b2d81d3",
          "nav_item_WdEafC",
          "nav_item_dJqYG3",
          "nav_item_XBwnrh",
          "nav_item_ct7Fd3",
          "nav_item_qDcmep",
          "nav_item_9LAFYi",
          "nav_item_YVXXp4",
          "nav_item_8zYyKp",
          "nav_item_GdtyUb",
          "nav_item_GzayEY",
          "nav_item_DGBUnN",
          "nav_item_ngg6Dm",
          "nav_item_8J4UkU",
          "nav_item_U47YNU",
          "nav_item_XTQ4Dg",
          "nav_item_kQ98MV",
          "nav_item_HDTPE6",
          "content_item_pE3fYQ",
          "adb63f00-9f5b-46ce-8474-ab08ac6b646d",
          "455693c9-1a68-4851-afe8-dfe7a4b27b41",
          "8cb307a3-b1a2-4729-b5d6-f83a195639a2",
          "09e48b1e-f439-402d-87e0-1e55465f13a0",
          "c59a01c5-0b2b-440e-859d-480a52de5f59",
          "306f11a6-e074-4e04-8bc2-004697082c15",
          "a91be5be-30f1-4964-aba3-7915356419c1",
          "a1574118-b48e-4862-aa4b-e64d155f5b3e",
          "fcd6b399-774b-4a02-b4b8-7e981ba7ffcb",
          "406632ee-8b72-4d9d-9437-45f5c6f9e173",
          "c03557f1-e54f-4fe2-8140-e0e17f9e2ad7"
        ],
        "custom_css": [
          ".nav-item__media {height: 200px;}"
        ],
        "settings": {
          "menu_text": "Sale",
          "color_selector": "#000000"
        }
      },
      "slider-cart": {
        "type": "slider-cart",
        "blocks": {
          "frame_8Y9dfr": {
            "type": "frame",
            "settings": {
              "title": "⤷ Cart Header",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#ffffff",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-right layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-md",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-lg",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": "sticky top-0 left-0 z-10 border-b"
            }
          },
          "frame_km84NC": {
            "type": "frame",
            "settings": {
              "title": "⤷ Text Container",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "rgba(0,0,0,0)",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": "relative"
            }
          },
          "frame_FptPGw": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.item_count > 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "cart_header_iDNpEb": {
            "type": "cart-header",
            "disabled": true,
            "settings": {
              "header_classes": "",
              "header_text_classes": "",
              "content": "<div class=\"flex justify-center items-center\">\n  <svg class=\"icon icon-icon_name text-black\" style=\"\" width=\"24\" height=\"24\" fill=\"currentColor\" stroke=\"currentColor\" stroke-width=\"0\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n    <use xlink:href=\"#icon-def-cart\"></use>\n  </svg>\n</div>",
              "close": true,
              "close_button_classes": "",
              "icon_size": 29,
              "display": "icon"
            }
          },
          "break_ghxKPd": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "title_4B4brM": {
            "type": "title",
            "disabled": true,
            "settings": {
              "title_text": "",
              "title_liquid": "",
              "title_attr_x_text": "$store.cart.item_count < 1 ? 'Your Cart Is Empty' : ''",
              "title_element": "h3",
              "title_class_type_style": "type-section",
              "title_class_type_size": "type--sm",
              "title_style_color": ""
            }
          },
          "title_b3gB4H": {
            "type": "title",
            "settings": {
              "title_text": "",
              "title_liquid": "",
              "title_attr_x_text": "$store.cart.item_count > 0 ? 'Your Bag': 'Your Bag Is Empty'",
              "title_element": "p",
              "title_class_type_style": "type-item",
              "title_class_type_size": "",
              "title_style_color": "#000000"
            }
          },
          "frame_UVUAcK": {
            "type": "frame",
            "settings": {
              "title": "⤷ Button Container",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-auto",
              "article_class_width_desktop": "lg:w-auto",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-right layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "lg:px-lg",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "absolute right-0"
            }
          },
          "button_hTWGcX": {
            "type": "button",
            "settings": {
              "style": "button--icon",
              "button_class_size": "",
              "button_text": "",
              "leading_icon": "",
              "trailing_icon": "x",
              "link": "",
              "onclick": "Modal.close()",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "border-none",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "break_FH9Vkb": {
            "type": "break",
            "settings": {
              "title": "⤶ Button Container"
            }
          },
          "break_YA3mRz": {
            "type": "break",
            "settings": {
              "title": "⤶ Text Container"
            }
          },
          "spacer_frMKDX": {
            "type": "spacer",
            "settings": {
              "spacer_class_vertical_spacing": "pb-md",
              "spacer_class_horizontal_spacing": "@include Spacing prop:pr",
              "spacer_class_vertical_spacing_desktop": "lg:pb-lg",
              "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"
            }
          },
          "frame_igdKyf": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "px-lg",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "lg:px-xl",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": ""
            }
          },
          "break_DPziDR": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_mknhyD": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "offer_progress_EP9gbN": {
            "type": "offer_progress",
            "settings": {
              "title": "Offer Progress",
              "key": "free",
              "combinable": true,
              "combinable_empty_cart": false,
              "offer_style_background_color": "#ffffff",
              "offer_style_background": "",
              "offer_progress_style_background_color": "#ededed",
              "offer_progress_bar_style_background_color": "#000000",
              "offer_threshold_passed_style_color": "#ffffff",
              "offer_threshold_label_style_color": "#000000",
              "offer_class_display": "flex",
              "offer_class_display_desktop": "lg:flex",
              "offer_class_direction": "flex-col",
              "offer_class_layout": "layout-center layout-top",
              "offer_class_layout_spacing": "layout-space-packed",
              "offer_class_vertical_padding": "py-md",
              "offer_class_horizontal_padding": "px-xl",
              "offer_class_gap": "gap-xl",
              "offer_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "offer_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "offer_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "offer_message_class_type_style": "type-micro",
              "offer_message_class_type_size": "",
              "offer_message_style_color": "#000000"
            }
          },
          "offer_progress_RGhxad": {
            "type": "offer_progress",
            "disabled": true,
            "settings": {
              "title": "Offer Progress",
              "key": "freeshipsolo",
              "combinable": false,
              "combinable_empty_cart": false,
              "offer_style_background_color": "",
              "offer_style_background": "",
              "offer_progress_style_background_color": "#eeece1",
              "offer_progress_bar_style_background_color": "#0066d2",
              "offer_threshold_passed_style_color": "#ffffff",
              "offer_threshold_label_style_color": "#000000",
              "offer_class_display": "flex",
              "offer_class_display_desktop": "lg:flex",
              "offer_class_direction": "flex-col",
              "offer_class_layout": "layout-center layout-top",
              "offer_class_layout_spacing": "layout-space-packed",
              "offer_class_vertical_padding": "@include Spacing prop:py",
              "offer_class_horizontal_padding": "@include Spacing prop:px",
              "offer_class_gap": "gap-sm",
              "offer_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "offer_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "offer_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "offer_message_class_type_style": "@include TypeStyle",
              "offer_message_class_type_size": "@include TypeSize",
              "offer_message_style_color": ""
            }
          },
          "break_GYm9LT": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_Mqp9xn": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": ""
            }
          },
          "offer_progress_hJWFDg": {
            "type": "offer_progress",
            "settings": {
              "title": "Offer Progress",
              "key": "canada",
              "combinable": true,
              "combinable_empty_cart": false,
              "offer_style_background_color": "#ffffff",
              "offer_style_background": "",
              "offer_progress_style_background_color": "#ededed",
              "offer_progress_bar_style_background_color": "#000000",
              "offer_threshold_passed_style_color": "#ffffff",
              "offer_threshold_label_style_color": "#000000",
              "offer_class_display": "flex",
              "offer_class_display_desktop": "lg:flex",
              "offer_class_direction": "flex-col",
              "offer_class_layout": "layout-center layout-top",
              "offer_class_layout_spacing": "layout-space-packed",
              "offer_class_vertical_padding": "py-md",
              "offer_class_horizontal_padding": "px-xl",
              "offer_class_gap": "gap-xl",
              "offer_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "offer_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "offer_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "offer_message_class_type_style": "type-micro",
              "offer_message_class_type_size": "",
              "offer_message_style_color": "#000000"
            }
          },
          "break_NnkGyL": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_FmTEdF": {
            "type": "break",
            "settings": {
              "title": "⤶ Cart Header"
            }
          },
          "frame_jUa8Xh": {
            "type": "frame",
            "settings": {
              "title": "⤷ Empty State",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length == 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#f5f5f5",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-md",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "lg:py-lg",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "title_9BWN8H": {
            "type": "title",
            "settings": {
              "title_text": "Explore Our Collections",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "h5",
              "title_class_type_style": "type-body",
              "title_class_type_size": "",
              "title_style_color": "#000000"
            }
          },
          "frame_dpnqUB": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "gap-md",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-md",
              "article_class_custom_classes": ""
            }
          },
          "button_fPcKqF": {
            "type": "button",
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "New Arrivals",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/new-arrivals",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_TpAJBn": {
            "type": "button",
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "Most Popular",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/most-popular",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_KjKgBg": {
            "type": "button",
            "disabled": true,
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "Father's Day Gifts",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://pages/fathers-day-gifts",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_iK98rc": {
            "type": "button",
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "Find Your Fit Quiz",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/fit-finder",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "break_wk3jmi": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_RE6Jkr": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "custom_liquid_iYXgma": {
            "type": "custom_liquid",
            "settings": {
              "liquid": "<style>\n  .minicart_icon {\n    height: 65px;\n    width: 65px;\n  }\n  @media only screen and (min-width: 1023px) {\n    .minicart_icon {\n      height: 40px;\n      width: 40px;\n    }\n  }\n</style>\n<div class=\"minicart_icon_and_text grid grid-cols-2 lg:grid-cols-4 lg:p-xl text-center text-xs gap-md\">\n  <div class=\"minicart_section flex flex-col items-center gap-2xs\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Shipping.png\n\t\"\n      alt=\"free shipping\"\n      class=\"minicart_icon\"\n    />\n    <p>Free Shipping</p>\n  </div>\n  <div class=\"minicart_section flex flex-col items-center gap-2xs\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Returns.png\"\n      alt=\"second icon\"\n      class=\"minicart_icon\"\n    />\n    <p>Free Exchanges</p>\n  </div>\n  <div class=\"minicart_section flex flex-col items-center gap-2xs\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/PDP_Icon_-_80x80_3.png?v=1750873108\"\n      alt=\"warranty icon\"\n      class=\"minicart_icon\"\n    />\n    <p>Limited Lifetime Warranty</p>\n  </div>\n  <div class=\"minicart_section flex flex-col items-center gap-2xs\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Perfect_Fit_Promise_796cc8af-d354-4891-bbb9-4310328c14b7.png\"\n      alt=\"third icon\"\n      class=\"minicart_icon\"\n    />\n    <p>Perfect Fit Promise</p>\n  </div>\n</div>"
            }
          },
          "break_dXB6hi": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_njhaec": {
            "type": "break",
            "settings": {
              "title": "⤶ Empty State"
            }
          },
          "frame_TKNDg6": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length > 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": ""
            }
          },
          "gift_message_mtzbag": {
            "type": "gift_message",
            "disabled": true,
            "settings": {}
          },
          "frame_XBkTLJ": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:grid lg:grid-cols-1",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "p-4"
            }
          },
          "break_cP6M89": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_Nd9ENw": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "2281212c-bd71-4dee-9195-3fe1892b4cc2": {
            "type": "line_items",
            "settings": {
              "empty_state": "",
              "item_badges": "item?.properties?._tags?.includes('best-seller')::Popular::#FFFFFF::#000000\nitem?.properties?._source == 'GWP'::Free 3 Hat Traveler::#000000::#FFFFFF\nitem?.sku.includes('PRESALE')::Expected Ship Date ${item.sku.split(`PRESALE:`).at(-1)}::#000000::#FFFFFF\n!item.sku.includes('PRESALE') && cart.items.some(i=>i.sku.includes('PRESALE'))::Ships Immediately::#000000::#FFFFFF",
              "badge_position": "bottom_option",
              "line_item_title_source": "item.title.split(' - ')[0]",
              "hide_second_option": false,
              "show_item_type": false,
              "show_combined_options": true,
              "line_item_type_source": "",
              "show_gift_card_reviews": false,
              "show_offer_options": false,
              "show_offer_price": false
            }
          },
          "frame_aUUVMC": {
            "type": "frame",
            "disabled": true,
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length > 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": ""
            }
          },
          "custom_liquid_WymVP6": {
            "type": "custom_liquid",
            "disabled": true,
            "settings": {
              "liquid": "<div class=\"minicart_icon_and_text grid grid-cols-3 p-xl text-center text-xs\"><div class=\"minicart_section flex flex-col items-center gap-2xs\"><img src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Free_Shipping.webp?width=30&height=30\n\t\" alt=\"free shipping\" class=\"minicart_icon\"> <p>Free Shipping</p></div> <div class=\"minicart_section flex flex-col items-center gap-2xs\"><img src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Free_Returns_1.webp?width=30&height=30\" alt=\"second icon\" class=\"minicart_icon\"> <p>Free Returns & Exchanges</p></div> <div class=\"minicart_section flex flex-col items-center gap-2xs\"><img src=\"https://cdn.shopify.com/s/files/1/1175/0278/files/Perfect_Fit_Promise_1.webp?width=30&height=30\" alt=\"third icon\" class=\"minicart_icon\"> <p>Perfect Fit Promise</p></div></div>"
            }
          },
          "break_D7LN4Q": {
            "type": "break",
            "disabled": true,
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "product_recs_hgNENd": {
            "type": "product_recs",
            "settings": {
              "title": "You might also like",
              "url": "/collections/cart-upsell/products.json",
              "include_cart_recs": false,
              "inclusion_js": "",
              "map": "",
              "enhanced_upsell_item": true,
              "include_swatches": true,
              "include_review_summary": false,
              "slides_per_view": 1,
              "slides_space_between": 16,
              "slides_per_view_desktop": 1,
              "slides_space_between_desktop": 16,
              "arrows": true,
              "loop": false
            }
          },
          "frame_4P9L8z": {
            "type": "frame",
            "settings": {
              "title": "⤷ Footer",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length > 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#ffffff",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-md",
              "article_class_horizontal_padding": "px-md",
              "article_class_gap": "gap-md",
              "article_class_vertical_padding_desktop": "lg:py-md",
              "article_class_horizontal_padding_desktop": "lg:px-md",
              "article_class_gap_desktop": "lg:gap-md",
              "article_class_custom_classes": "sticky bottom-0 left-0 z-10 border-t"
            }
          },
          "offer_progress_summary_kUyg6Y": {
            "type": "offer_progress_summary",
            "settings": {
              "offer_combine_key": "ship",
              "threshold_icon_style_background_color": "#91ca6b",
              "threshold_icon_style_color": "#ffffff",
              "threshold_label_style_color": "#91ca6b"
            }
          },
          "frame_wjzqAF": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom_classes": ""
            }
          },
          "text_6kdJYK": {
            "type": "text",
            "settings": {
              "text_text": "Summary",
              "text_liquid": "",
              "text_attr_x_text": "",
              "text_element": "div",
              "text_class_type_style": "type-item",
              "text_class_type_size": "",
              "text_style_color": ""
            }
          },
          "text_LfVTMi": {
            "type": "text",
            "settings": {
              "text_text": "",
              "text_liquid": "",
              "text_attr_x_text": "money.format($store.cart.total_price).split('.')[0]",
              "text_element": "div",
              "text_class_type_style": "type-item",
              "text_class_type_size": "",
              "text_style_color": ""
            }
          },
          "break_ghqDre": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "button_MHwLWG": {
            "type": "button",
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "CHECKOUT",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/checkout",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "payment_widget_gy8WiQ": {
            "type": "payment_widget",
            "settings": {
              "payment_count": 4,
              "text": "Or [ count ] interest-free payments with ((shoppay 60x16)) or ((afterpay 75x16))",
              "link_1": "",
              "link_2": "",
              "link_3": "",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')"
            }
          },
          "payment_widget_KDRxaG": {
            "type": "payment_widget",
            "settings": {
              "payment_count": 4,
              "text": "Or [ count ] interest-free payments with ((shoppay 60x16))",
              "link_1": "",
              "link_2": "",
              "link_3": "",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')"
            }
          },
          "frame_EkPkgB": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "max-lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "title_LREkKb": {
            "type": "title",
            "settings": {
              "title_text": "FREE SHIPPING - FREE EXCHANGES - PERFECT FIT PROMISE",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "div",
              "title_class_type_style": "type-micro",
              "title_class_type_size": "type--sm",
              "title_style_color": ""
            }
          },
          "break_WM4AkJ": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_XnWJ8E": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "title_hAz6zX": {
            "type": "title",
            "settings": {
              "title_text": "FREE SHIPPING - FREE EXCHANGES - PERFECT FIT PROMISE",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "div",
              "title_class_type_style": "type-item",
              "title_class_type_size": "type--sm",
              "title_style_color": ""
            }
          },
          "break_RHVGib": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_VxhXpB": {
            "type": "break",
            "settings": {
              "title": "⤶ footer End"
            }
          }
        },
        "block_order": [
          "frame_8Y9dfr",
          "frame_km84NC",
          "frame_FptPGw",
          "cart_header_iDNpEb",
          "break_ghxKPd",
          "title_4B4brM",
          "title_b3gB4H",
          "frame_UVUAcK",
          "button_hTWGcX",
          "break_FH9Vkb",
          "break_YA3mRz",
          "spacer_frMKDX",
          "frame_igdKyf",
          "break_DPziDR",
          "frame_mknhyD",
          "offer_progress_EP9gbN",
          "offer_progress_RGhxad",
          "break_GYm9LT",
          "frame_Mqp9xn",
          "offer_progress_hJWFDg",
          "break_NnkGyL",
          "break_FmTEdF",
          "frame_jUa8Xh",
          "title_9BWN8H",
          "frame_dpnqUB",
          "button_fPcKqF",
          "button_TpAJBn",
          "button_KjKgBg",
          "button_iK98rc",
          "break_wk3jmi",
          "frame_RE6Jkr",
          "custom_liquid_iYXgma",
          "break_dXB6hi",
          "break_njhaec",
          "frame_TKNDg6",
          "gift_message_mtzbag",
          "frame_XBkTLJ",
          "break_cP6M89",
          "break_Nd9ENw",
          "2281212c-bd71-4dee-9195-3fe1892b4cc2",
          "frame_aUUVMC",
          "custom_liquid_WymVP6",
          "break_D7LN4Q",
          "product_recs_hgNENd",
          "frame_4P9L8z",
          "offer_progress_summary_kUyg6Y",
          "frame_wjzqAF",
          "text_6kdJYK",
          "text_LfVTMi",
          "break_ghqDre",
          "button_MHwLWG",
          "payment_widget_gy8WiQ",
          "payment_widget_KDRxaG",
          "frame_EkPkgB",
          "title_LREkKb",
          "break_WM4AkJ",
          "frame_XnWJ8E",
          "title_hAz6zX",
          "break_RHVGib",
          "break_VxhXpB"
        ],
        "custom_css": [
          ".slider-cart__header {background-color: #fff; padding-bottom: 0px; padding-top: 0px;}",
          ".icon-x {color: #000;}",
          ".slider-cart__header-content .icon-x {color: #000;}",
          ".slider-cart__buttons {padding-bottom: 1rem !important;}",
          ".payments-banner {padding: 0px;}",
          ".slider-cart__payment-widget {padding-bottom: 0px;}",
          ".type-item.type--sm {font-size: 9px;}"
        ],
        "settings": {
          "classes": "",
          "container_classes": "flex flex-col h-full"
        }
      },
      "redirects": {
        "type": "redirects",
        "settings": {}
      },
      "subnav": {
        "type": "subnav",
        "settings": {
          "section_classes": "hidden",
          "menu": "main-menu",
          "dynamic": true
        }
      },
      "search-modal": {
        "type": "search-modal",
        "blocks": {
          "906581cd-b753-461c-8dc6-f3fcab58a1dd": {
            "type": "data-boost",
            "settings": {}
          }
        },
        "block_order": [
          "906581cd-b753-461c-8dc6-f3fcab58a1dd"
        ],
        "settings": {
          "form": false,
          "debounce": 300,
          "limit": 9,
          "product_grid_classes": "w-3/4 p-5 mt-6 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8",
          "product_filters_classes": "p-5 w-1/4 sticky top-24",
          "paging": "scroll",
          "sort": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price",
          "search_item_title_logic": "product.title",
          "search_item_type_logic": "product.type",
          "search_item_sku_logic": "product.sku.split('|').at(0)",
          "show_search_suggestions": true,
          "hide_collections_nonprimary_market": "hide",
          "search_suggestions": "thermal, hydro, adventure club, odysea, golf, beanies",
          "search_suggestions_heading_label": "Suggestions",
          "product_suggestions_type": "bestsellers",
          "product_list": [],
          "product_suggestions_heading_label": "Trending Products",
          "search_redirects": true,
          "search_redirects_map": "gifts:/pages/gifts\nholiday gifts:/pages/gifts\nfit guide:/pages/fit-guide\nfit:/pages/fit-guide\nhealey:/blogs/news/mark-healeys-signature-melin-the-shore\nmark healey:/blogs/news/mark-healeys-signature-melin-the-shore\nshore:/blogs/news/mark-healeys-signature-melin-the-shore\nthe shore:/blogs/news/mark-healeys-signature-melin-the-shore\ntraeger:/blogs/news/melin-x-traeger-good-food-good-mood\nlinks:/collections/links-collection\nwacks:/collections/waxd-collection\nwax:/collections/waxd-collection\nwaxed:/collections/waxd-collection\ncinco:/collections/collections\ncontla:/collections/collections\ngum:/collections/collections\ngum vintage:/collections/collections\ncard:/products/melin-e-gift-card?variant=16338350470\ngift card:/products/melin-e-gift-card?variant=16338350470\ne-gift card:/products/melin-e-gift-card?variant=16338350470\nbrady:/blogs/news/tom-bradys-tb12-partners-with-melin\ntb12:/blogs/news/tom-bradys-tb12-partners-with-melin\ntom:/blogs/news/tom-bradys-tb12-partners-with-melin\ntom brady:/blogs/news/tom-bradys-tb12-partners-with-melin\nevening:/collections/evening-fog-collection\nevening fog:/collections/evening-fog-collection\nfog:/collections/evening-fog-collection\ndesert:/collections/desert-nights-collection\ndesert night:/collections/desert-nights-collection\ndesert nights:/collections/desert-nights-collection\ntake it easy:/products/odyssey-mantra-hydro-navy\nmammoth:/blogs/news/win-a-trip-to-mammoth-mountain\nmammoth adventure:/blogs/news/win-a-trip-to-mammoth-mountain\nfaq:/https://support.melin.com/\nsupport:/https://support.melin.com/\nkings:/pages/special-projects\nnba:/pages/special-projects\nnfl:/pages/special-projects\nnhl:/pages/special-projects\nred bull:/pages/special-projects\ncup:/collections/americana-collection\nfootball:/collections/americana-collection\nsoccer:/collections/americana-collection\nworld:/collections/americana-collection\nworld cup:/collections/americana-collection\nflag:/collections/americana-collection\ntrucker:/melin.com/collections/odyssey\nkid:/collections/shop-all-hats\nkids:/collections/shop-all-hats\nmen:/collections/shop-all-hats\nmens:/collections/shop-all-hats\nunisex:/collections/shop-all-hats\nwomen:/collections/shop-all-hats\nwomens:/collections/shop-all-hats\nbucket:/collections/shop-all-hats\nfitted:/collections/shop-all-hats\nexchange:/https://melin.com/returns\nexchanges:/https://melin.com/returns\nreturn:/https://melin.com/returns\nreturns:/https://melin.com/returns\nhave more fun:/collections/coronado\nhmf:/collections/coronado\nhave:/collections/coronado\nmore:/collections/coronado\nfun:/collections/coronado\nkenny santucci:/products/odyssey-strong-hydro-black\nkenny:/products/odyssey-strong-hydro-black\nsantucci:/products/odyssey-strong-hydro-black\nstrong nyc:/products/odyssey-strong-hydro-black\nfriend:/pages/refer\nfriends:/pages/refer\nrefer:/pages/refer\nrefer a friend:/pages/refer\nreferral:/pages/refer\nphil:/products/a-game-hydro-waterproof-snapback-hat-black?variant=39472223322208\nphil mickelson:/products/a-game-hydro-waterproof-snapback-hat-black?variant=39472223322208\nmickelson:/products/a-game-hydro-waterproof-snapback-hat-black?variant=39472223322208\nkpmg:/products/a-game-hydro-waterproof-snapback-hat-black?variant=39472223322208\ncoral:/products/a-game-hydro-coral-gardeners-black\ncoral gardeners:/products/a-game-hydro-coral-gardeners-black\nblack out:/collections/black-out-collection\nblackout:/collections/black-out-collection\nattia:/products/a-game-outlive-hydro-black\npeter attia:/products/a-game-outlive-hydro-black\nroyal blue:/collections/royal-collection\nripstop:/collections/ripcord-collection\nodyssey:/collections/odysea\nolukai:/products/odysea-ohana-hydro-black?view=melin-x-olukai\nohana:/products/odysea-ohana-hydro-black?view=melin-x-olukai\nunited:/collections/americana-collection"
        }
      },
      "newsletter": {
        "type": "newsletter",
        "blocks": {
          "d9123f9a-1298-49a1-9454-75e6baac2533": {
            "type": "mini-form",
            "settings": {
              "inclusion_js": "true",
              "on_button_click": "",
              "form_method": "POST",
              "form_action": "",
              "prevent_default": true,
              "on_submit": "event.preventDefault();\nCustomer.identify('email', email.value); Customer.subscriptions.subscribe('news-and-offers','Footer Email Signup');\nklaviyo.identify({ '$email' : email.value });\n\nconst email_custom_source = 'Footer Email Sign Up for email';\nconst email_public_api = 'Ra4Ub5';\nconst email_list_id = 'Y6tVdb';\n\nconst myHeaders = new Headers();myHeaders.append('revision', '2024-02-15');myHeaders.append('Content-Type', 'application/json');\nconst raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email.value}}}},'relationships': {'list': {'data': {'type': 'list','id': email_list_id}}}}});\nconst requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};\nfetch('https://a.klaviyo.com/client/subscriptions/?company_id='+email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));",
              "field_type": "email",
              "trigger_text": "((mail 20x16)) SIGN UP FOR EMAILS",
              "field_name": "email",
              "placeholder_text": "Email Address",
              "input_pattern": "",
              "input_mask": "",
              "on_input": "",
              "activate_email_validation": false,
              "error_message": "",
              "submit_text": "SIGN UP",
              "button_style": "button--primary",
              "info_text": "<p>Sign up to receive 10% off your first order, and learn about exclusive events, news, and collaborations!</p>",
              "success_text": "<p>We’ve sent you an email to verify <br/>your address.</p>"
            }
          },
          "9504fa8f-01a6-4101-b726-ae0bf78b234f": {
            "type": "mini-form",
            "settings": {
              "inclusion_js": "!window.location.hostname.includes(\"melinbrand.ca\")",
              "on_button_click": "event.preventDefault();\nif (window.innerWidth >= 760) {\n    window.__attentive.trigger(null, null, null, 433361);\n  } else {\n    window.__attentive.trigger(null, null, null, 431792);\n  }",
              "form_method": "POST",
              "form_action": "",
              "prevent_default": true,
              "on_submit": "",
              "field_type": "tel",
              "trigger_text": "((chat 20x16)) SIGN UP FOR TEXTS",
              "field_name": "phone",
              "placeholder_text": "Mobile Number",
              "input_pattern": "",
              "input_mask": "(999) - 999-9999",
              "on_input": "",
              "activate_email_validation": false,
              "error_message": "",
              "submit_text": "SIGN UP",
              "button_style": "button--primary",
              "info_text": "<p>Unlock EARLY access to our exclusive products when you sign up for texts.</p><p>By submitting this form, you agree to receive recurring automated and promotional marketing text messages (e.g. cart reminders) from OluKai at the cell number used when signing up. Reply HELP for help and STOP to cancel. Msg frequency varies. Msg & data rates apply. View <a href=\"https://olukai-store.myshopify.com/pages/messaging-terms-conditions\" target=\"_blank\" title=\"Messaging Terms & Conditions\">Terms</a> & <a href=\"https://olukai-store.myshopify.com/pages/messaging-privacy-policy\" target=\"_blank\" title=\"Messaging Privacy Policy\">Privacy</a>.</p>",
              "success_text": "<p>Reply “YES” to confirm your subscription.</p>"
            }
          }
        },
        "block_order": [
          "d9123f9a-1298-49a1-9454-75e6baac2533",
          "9504fa8f-01a6-4101-b726-ae0bf78b234f"
        ],
        "custom_css": [],
        "settings": {
          "wrapper_style_background_color": "",
          "wrapper_style_background": "",
          "wrapper_class_vertical_padding": "py-xl",
          "wrapper_class_horizontal_padding": "",
          "wrapper_class_vertical_padding_desktop": "lg:py-3xl",
          "wrapper_class_horizontal_padding_desktop": "lg:px-7xl",
          "container_class_container": "container",
          "container_class_vertical_padding": "py-0",
          "container_class_horizontal_padding": "px-0",
          "container_class_vertical_padding_desktop": "lg:py-0",
          "container_class_horizontal_padding_desktop": "lg:px-0",
          "text_item_1_text": "JOIN THE MELIN FAMILY",
          "text_item_1_liquid": "",
          "text_item_1_attr_x_text": "",
          "text_item_1_element": "h2",
          "text_item_1_class_type_style": "type-section",
          "text_item_1_class_type_size": "",
          "text_item_1_style_color": "",
          "text_item_2_text": "Enjoy 10% off your first order and be the first to know about product releases, member-only offers, and more.",
          "text_item_2_liquid": "",
          "text_item_2_attr_x_text": "",
          "text_item_2_element": "p",
          "text_item_2_class_type_style": "type-body",
          "text_item_2_class_type_size": "",
          "text_item_2_style_color": ""
        }
      },
      "offers": {
        "type": "offers",
        "blocks": {
          "offer_JB4Wr9": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "Free Exchanges",
              "key": "freeexchange",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "cart.item_count > 3",
              "progress_numerator": "cart.item_count",
              "progress_denominator": "3",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're 1 product away from FREE exchanges!",
              "success": "Your bag qualifies for FREE exchanges",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n  <style type=\"text/css\">\n    .st0 { fill: currentColor; stroke: currentColor; }\n    .st1 { fill: none; stroke: currentColor;}\n    .st2 { fill: none; stroke: currentColor; stroke-width: 1.0103; stroke-miterlimit: 10; }\n  </style>\n  <g id=\"Group_1427\" transform=\"translate(0.75 0.75)\">\n    <g id=\"Group_1426\" transform=\"translate(0.462 13.577)\">\n      <path id=\"Path_1779\" class=\"st0\" d=\"M19.643,32.445l-8.989-8.671l0.202,9.703l17.795,8.633l-0.202-9.703L19.643,32.445z M15.381,34.386l-2.956-1.435v-5.649l2.988,2.932L15.381,34.386z M19.243,36.258l-3.121-1.513L16.16,30.9l3.15,2.938L19.243,36.258z M23.089,38.123l-3.113-1.508l0.067-2.42l3.083,0.086L23.089,38.123z M26.794,39.92l-2.963-1.435l0.031-4.157l2.932-0.056L26.794,39.92z\"/>\n      <g id=\"Group_1425\" transform=\"translate(0.704 21.107)\">\n        <polygon class=\"st0\" points=\"5.263,-12.041 4.823,-11.133 31.985,2.046 31.985,34.547 32.996,34.547 32.996,1.413\"/>\n        <rect x=\"30.96\" y=\"-5.433\" transform=\"matrix(0.8997 -0.4365 0.4365 0.8997 6.7863 19.6782)\" class=\"st0\" width=\"30.507\" height=\"1.01\"/>\n      </g>\n      <g id=\"Path_1781\" transform=\"translate(-918.465 -1221.502)\">\n        <path class=\"st1\" d=\"M951.367,1216.86l-27.34,13.751c-0.178,0.089-0.289,0.272-0.289,0.471v32.623c0,0.201,0.115,0.384,0.296,0.471l27.343,13.246c0.145,0.07,0.314,0.07,0.459,0l27.34-13.248c0.181-0.087,0.296-0.271,0.296-0.471v-32.621c0.001-0.199-0.111-0.382-0.289-0.471l-27.341-13.751C951.692,1216.785,951.516,1216.785,951.367,1216.86z\"/>\n        <path class=\"st0\" d=\"M979.18,1230.612l-27.341-13.751c-0.074-0.036-0.155-0.056-0.236-0.056c-0.081,0-0.162,0.018-0.237,0.056l-27.34,13.751c-0.177,0.089-0.289,0.271-0.289,0.47v32.623c0,0.201,0.114,0.385,0.296,0.472l27.34,13.248c0.145,0.07,0.314,0.07,0.459,0l27.34-13.248c0.181-0.088,0.296-0.271,0.296-0.472v-32.623C979.468,1230.882,979.357,1230.701,979.18,1230.612z M978.458,1263.399l-26.855,13.014l-26.855-13.013v-32.02l26.854-13.507l26.856,13.507V1263.399L978.458,1263.399z\"/>\n      </g>\n      <path class=\"st0\" d=\"M51.559,13.288L24.178,0.002l-0.441,0.909L50.9,14.091v12.266l-1.741-1.368l-1.096,2.847l-1.34-2.195l-1.612,2.303V17.252c0-0.237-0.138-0.458-0.352-0.561L17.378,3.407l-0.441,0.909l27.163,13.178v13.656l2.564-3.664l1.604,2.626l1.341-3.484l2.3,1.807V13.848C51.911,13.607,51.769,13.386,51.559,13.288z\"/>\n      <path id=\"Path_1783\" class=\"st2\" d=\"M44.604,29.002\"/>\n    </g>\n    <path class=\"st2\" d=\"M33.599,79.25c-9.983,0.001-19.963-3.745-27.741-11.24c-0.201-0.193-0.207-0.513-0.013-0.715c0.194-0.2,0.514-0.207,0.715-0.013c15.162,14.608,38.917,14.607,54.08,0c7.497-7.222,11.733-16.932,11.927-27.34s-3.677-20.27-10.9-27.767C46.756-3.302,22.035-3.762,6.559,11.148c-0.201,0.194-0.521,0.188-0.715-0.013c-0.194-0.201-0.188-0.521,0.013-0.715c15.878-15.297,41.24-14.824,56.536,1.054c7.41,7.692,11.381,17.808,11.182,28.486C73.377,50.639,69.031,60.6,61.339,68.01C53.563,75.503,43.579,79.25,33.599,79.25z\"/>\n    <path class=\"st2\" d=\"M15.774,12.271H6.291c-0.759,0-1.376-0.617-1.376-1.376V1.414c0-0.279,0.226-0.505,0.505-0.505s0.505,0.226,0.505,0.505v9.481c0,0.201,0.164,0.365,0.365,0.365h9.483c0.279,0,0.505,0.226,0.505,0.505S16.053,12.271,15.774,12.271z\"/>\n  </g>\n</svg>",
              "threshold_label": "Free Exchanges"
            }
          },
          "offer_T8UU3A": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "Free Shipping",
              "key": "freeship",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "cart.item_count > 6",
              "progress_numerator": "cart.item_count",
              "progress_denominator": "6",
              "action_filter": "",
              "delay": 1,
              "actions": "console.log(cart.item_count, 'testtttinng')",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're <strong> ${2 - cart.item_count} </strong> away from FREE Shipping!",
              "success": "Your bag qualifies for $${(2000 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} shipping!",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n\t.st0{fill: currentColor;}\n</style>\n<g>\n\t<path class=\"st0\" d=\"M66.068,27.798H53.244V15.476H0v40.965h6.663c0.521,4.958,4.963,8.554,9.92,8.033   c4.238-0.446,7.587-3.794,8.033-8.033h32.086c0.521,4.958,4.963,8.554,9.92,8.033c4.238-0.446,7.587-3.794,8.033-8.033H80V41.729   L66.068,27.798z M22.712,56.441c-0.53,3.906-4.127,6.643-8.033,6.113c-3.533-0.48-6.17-3.492-6.178-7.057   c0-3.942,3.196-7.138,7.138-7.138s7.138,3.196,7.138,7.138c-0.002,0.316-0.025,0.631-0.068,0.944H22.712z M51.355,54.553H24.615   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033H1.886V17.361h49.469V54.553z M72.751,56.441   c-0.531,3.906-4.127,6.643-8.034,6.113c-3.533-0.48-6.17-3.492-6.178-7.057c0.04-3.942,3.267-7.105,7.209-7.066   c3.886,0.039,7.027,3.18,7.066,7.066C72.815,55.813,72.794,56.128,72.751,56.441z M74.653,54.553   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033h-3.457V29.686h12.042l12.825,12.825l0.001,12.043   H74.653z\"/>\n\t<path class=\"st0\" d=\"M34.489,35.582l-0.018-8.721l-7.851,3.932l-7.867-3.898l0.018,8.721L34.489,35.582z M32.967,29.287l0.01,5.273   l-2.615,0.007l-0.009-3.931L32.967,29.287z M29.697,30.917l0.008,3.65l-2.749,0.006l-0.005-2.369L29.697,30.917z M26.302,32.205   l0.005,2.369l-2.756,0.006l-0.007-3.65L26.302,32.205z M22.887,30.652l0.009,3.931l-2.611,0.005l-0.012-5.274L22.887,30.652z\"/>\n</g>\n</svg>",
              "threshold_label": "Free Shipping"
            }
          },
          "offer_wPAwC6": {
            "type": "offer",
            "settings": {
              "title": "Free Exchanges",
              "key": "freeexchange",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 4900",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "4900",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're 2 items away from <strong>FREE exchanges!</strong>",
              "success": "Your bag qualifies for <strong>FREE exchanges</strong>",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n  <style type=\"text/css\">\n    .st0 { fill: currentColor; stroke: currentColor; }\n    .st1 { fill: none; stroke: currentColor;}\n    .st2 { fill: none; stroke: currentColor; stroke-width: 1.0103; stroke-miterlimit: 10; }\n  </style>\n  <g id=\"Group_1427\" transform=\"translate(0.75 0.75)\">\n    <g id=\"Group_1426\" transform=\"translate(0.462 13.577)\">\n      <path id=\"Path_1779\" class=\"st0\" d=\"M19.643,32.445l-8.989-8.671l0.202,9.703l17.795,8.633l-0.202-9.703L19.643,32.445z M15.381,34.386l-2.956-1.435v-5.649l2.988,2.932L15.381,34.386z M19.243,36.258l-3.121-1.513L16.16,30.9l3.15,2.938L19.243,36.258z M23.089,38.123l-3.113-1.508l0.067-2.42l3.083,0.086L23.089,38.123z M26.794,39.92l-2.963-1.435l0.031-4.157l2.932-0.056L26.794,39.92z\"/>\n      <g id=\"Group_1425\" transform=\"translate(0.704 21.107)\">\n        <polygon class=\"st0\" points=\"5.263,-12.041 4.823,-11.133 31.985,2.046 31.985,34.547 32.996,34.547 32.996,1.413\"/>\n        <rect x=\"30.96\" y=\"-5.433\" transform=\"matrix(0.8997 -0.4365 0.4365 0.8997 6.7863 19.6782)\" class=\"st0\" width=\"30.507\" height=\"1.01\"/>\n      </g>\n      <g id=\"Path_1781\" transform=\"translate(-918.465 -1221.502)\">\n        <path class=\"st1\" d=\"M951.367,1216.86l-27.34,13.751c-0.178,0.089-0.289,0.272-0.289,0.471v32.623c0,0.201,0.115,0.384,0.296,0.471l27.343,13.246c0.145,0.07,0.314,0.07,0.459,0l27.34-13.248c0.181-0.087,0.296-0.271,0.296-0.471v-32.621c0.001-0.199-0.111-0.382-0.289-0.471l-27.341-13.751C951.692,1216.785,951.516,1216.785,951.367,1216.86z\"/>\n        <path class=\"st0\" d=\"M979.18,1230.612l-27.341-13.751c-0.074-0.036-0.155-0.056-0.236-0.056c-0.081,0-0.162,0.018-0.237,0.056l-27.34,13.751c-0.177,0.089-0.289,0.271-0.289,0.47v32.623c0,0.201,0.114,0.385,0.296,0.472l27.34,13.248c0.145,0.07,0.314,0.07,0.459,0l27.34-13.248c0.181-0.088,0.296-0.271,0.296-0.472v-32.623C979.468,1230.882,979.357,1230.701,979.18,1230.612z M978.458,1263.399l-26.855,13.014l-26.855-13.013v-32.02l26.854-13.507l26.856,13.507V1263.399L978.458,1263.399z\"/>\n      </g>\n      <path class=\"st0\" d=\"M51.559,13.288L24.178,0.002l-0.441,0.909L50.9,14.091v12.266l-1.741-1.368l-1.096,2.847l-1.34-2.195l-1.612,2.303V17.252c0-0.237-0.138-0.458-0.352-0.561L17.378,3.407l-0.441,0.909l27.163,13.178v13.656l2.564-3.664l1.604,2.626l1.341-3.484l2.3,1.807V13.848C51.911,13.607,51.769,13.386,51.559,13.288z\"/>\n      <path id=\"Path_1783\" class=\"st2\" d=\"M44.604,29.002\"/>\n    </g>\n    <path class=\"st2\" d=\"M33.599,79.25c-9.983,0.001-19.963-3.745-27.741-11.24c-0.201-0.193-0.207-0.513-0.013-0.715c0.194-0.2,0.514-0.207,0.715-0.013c15.162,14.608,38.917,14.607,54.08,0c7.497-7.222,11.733-16.932,11.927-27.34s-3.677-20.27-10.9-27.767C46.756-3.302,22.035-3.762,6.559,11.148c-0.201,0.194-0.521,0.188-0.715-0.013c-0.194-0.201-0.188-0.521,0.013-0.715c15.878-15.297,41.24-14.824,56.536,1.054c7.41,7.692,11.381,17.808,11.182,28.486C73.377,50.639,69.031,60.6,61.339,68.01C53.563,75.503,43.579,79.25,33.599,79.25z\"/>\n    <path class=\"st2\" d=\"M15.774,12.271H6.291c-0.759,0-1.376-0.617-1.376-1.376V1.414c0-0.279,0.226-0.505,0.505-0.505s0.505,0.226,0.505,0.505v9.481c0,0.201,0.164,0.365,0.365,0.365h9.483c0.279,0,0.505,0.226,0.505,0.505S16.053,12.271,15.774,12.271z\"/>\n  </g>\n</svg>",
              "threshold_label": "Free Exchanges"
            }
          },
          "offer_yNfTp9": {
            "type": "offer",
            "settings": {
              "title": "Free Shipping",
              "key": "freeship",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "!window.location.hostname.includes('melinbrand.ca')",
              "conditions": "(Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 12000))",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "12000",
              "action_filter": "!window.location.hostname.includes('melinbrand.ca')",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "",
              "approach": "You're 1 item away from <strong>FREE Shipping!</strong>",
              "success": "Your bag qualifies for <strong>FREE shipping!</strong>",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n\t.st0{fill: currentColor;}\n</style>\n<g>\n\t<path class=\"st0\" d=\"M66.068,27.798H53.244V15.476H0v40.965h6.663c0.521,4.958,4.963,8.554,9.92,8.033   c4.238-0.446,7.587-3.794,8.033-8.033h32.086c0.521,4.958,4.963,8.554,9.92,8.033c4.238-0.446,7.587-3.794,8.033-8.033H80V41.729   L66.068,27.798z M22.712,56.441c-0.53,3.906-4.127,6.643-8.033,6.113c-3.533-0.48-6.17-3.492-6.178-7.057   c0-3.942,3.196-7.138,7.138-7.138s7.138,3.196,7.138,7.138c-0.002,0.316-0.025,0.631-0.068,0.944H22.712z M51.355,54.553H24.615   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033H1.886V17.361h49.469V54.553z M72.751,56.441   c-0.531,3.906-4.127,6.643-8.034,6.113c-3.533-0.48-6.17-3.492-6.178-7.057c0.04-3.942,3.267-7.105,7.209-7.066   c3.886,0.039,7.027,3.18,7.066,7.066C72.815,55.813,72.794,56.128,72.751,56.441z M74.653,54.553   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033h-3.457V29.686h12.042l12.825,12.825l0.001,12.043   H74.653z\"/>\n\t<path class=\"st0\" d=\"M34.489,35.582l-0.018-8.721l-7.851,3.932l-7.867-3.898l0.018,8.721L34.489,35.582z M32.967,29.287l0.01,5.273   l-2.615,0.007l-0.009-3.931L32.967,29.287z M29.697,30.917l0.008,3.65l-2.749,0.006l-0.005-2.369L29.697,30.917z M26.302,32.205   l0.005,2.369l-2.756,0.006l-0.007-3.65L26.302,32.205z M22.887,30.652l0.009,3.931l-2.611,0.005l-0.012-5.274L22.887,30.652z\"/>\n</g>\n</svg>",
              "threshold_label": "Free Shipping"
            }
          },
          "offer_XJBGYJ": {
            "type": "offer",
            "settings": {
              "title": "Free Shipping",
              "key": "canada",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "window.location.hostname.includes('melinbrand.ca')",
              "conditions": "(Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price >= 15000))",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "15000",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "",
              "approach": "You're $${(15000 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from <strong>FREE Shipping!</strong>",
              "success": "Your bag qualifies for <strong>FREE shipping!</strong>",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n\t.st0{fill: currentColor;}\n</style>\n<g>\n\t<path class=\"st0\" d=\"M66.068,27.798H53.244V15.476H0v40.965h6.663c0.521,4.958,4.963,8.554,9.92,8.033   c4.238-0.446,7.587-3.794,8.033-8.033h32.086c0.521,4.958,4.963,8.554,9.92,8.033c4.238-0.446,7.587-3.794,8.033-8.033H80V41.729   L66.068,27.798z M22.712,56.441c-0.53,3.906-4.127,6.643-8.033,6.113c-3.533-0.48-6.17-3.492-6.178-7.057   c0-3.942,3.196-7.138,7.138-7.138s7.138,3.196,7.138,7.138c-0.002,0.316-0.025,0.631-0.068,0.944H22.712z M51.355,54.553H24.615   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033H1.886V17.361h49.469V54.553z M72.751,56.441   c-0.531,3.906-4.127,6.643-8.034,6.113c-3.533-0.48-6.17-3.492-6.178-7.057c0.04-3.942,3.267-7.105,7.209-7.066   c3.886,0.039,7.027,3.18,7.066,7.066C72.815,55.813,72.794,56.128,72.751,56.441z M74.653,54.553   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033h-3.457V29.686h12.042l12.825,12.825l0.001,12.043   H74.653z\"/>\n\t<path class=\"st0\" d=\"M34.489,35.582l-0.018-8.721l-7.851,3.932l-7.867-3.898l0.018,8.721L34.489,35.582z M32.967,29.287l0.01,5.273   l-2.615,0.007l-0.009-3.931L32.967,29.287z M29.697,30.917l0.008,3.65l-2.749,0.006l-0.005-2.369L29.697,30.917z M26.302,32.205   l0.005,2.369l-2.756,0.006l-0.007-3.65L26.302,32.205z M22.887,30.652l0.009,3.931l-2.611,0.005l-0.012-5.274L22.887,30.652z\"/>\n</g>\n</svg>",
              "threshold_label": "Free Shipping"
            }
          },
          "offer_jTENab": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "Discounted Expedited",
              "key": "freediscountexpedited",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price * item.quantity : 0)) + cart.total_price > 10500",
              "progress_numerator": "cart.total_price - cart.total_discount",
              "progress_denominator": "10500",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're <strong> $${(10600 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price * item.quantity : 0)) + cart.total_price))/100} </strong> away from Discounted EXPEDITED Shipping!",
              "success": "Your bag qualifies for <strong>DISCOUNTED</strong> Expedited Shipping!",
              "threshold_icon": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" id=\"Layer_1\" x=\"0px\" y=\"0px\" width=\"24px\" height=\"24px\" viewBox=\"0 0 80 80\" style=\"enable-background:new 0 0 80 80;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n\t.st0{fill: currentColor;}\n</style>\n<g>\n\t<path class=\"st0\" d=\"M66.068,27.798H53.244V15.476H0v40.965h6.663c0.521,4.958,4.963,8.554,9.92,8.033   c4.238-0.446,7.587-3.794,8.033-8.033h32.086c0.521,4.958,4.963,8.554,9.92,8.033c4.238-0.446,7.587-3.794,8.033-8.033H80V41.729   L66.068,27.798z M22.712,56.441c-0.53,3.906-4.127,6.643-8.033,6.113c-3.533-0.48-6.17-3.492-6.178-7.057   c0-3.942,3.196-7.138,7.138-7.138s7.138,3.196,7.138,7.138c-0.002,0.316-0.025,0.631-0.068,0.944H22.712z M51.355,54.553H24.615   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033H1.886V17.361h49.469V54.553z M72.751,56.441   c-0.531,3.906-4.127,6.643-8.034,6.113c-3.533-0.48-6.17-3.492-6.178-7.057c0.04-3.942,3.267-7.105,7.209-7.066   c3.886,0.039,7.027,3.18,7.066,7.066C72.815,55.813,72.794,56.128,72.751,56.441z M74.653,54.553   c-0.521-4.958-4.963-8.554-9.92-8.033c-4.238,0.446-7.587,3.794-8.033,8.033h-3.457V29.686h12.042l12.825,12.825l0.001,12.043   H74.653z\"/>\n\t<path class=\"st0\" d=\"M34.489,35.582l-0.018-8.721l-7.851,3.932l-7.867-3.898l0.018,8.721L34.489,35.582z M32.967,29.287l0.01,5.273   l-2.615,0.007l-0.009-3.931L32.967,29.287z M29.697,30.917l0.008,3.65l-2.749,0.006l-0.005-2.369L29.697,30.917z M26.302,32.205   l0.005,2.369l-2.756,0.006l-0.007-3.65L26.302,32.205z M22.887,30.652l0.009,3.931l-2.611,0.005l-0.012-5.274L22.887,30.652z\"/>\n</g>\n</svg>",
              "threshold_label": "Discounted Expedited Shipping"
            }
          },
          "offer_t4ccQE": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "GWP | BFCM",
              "key": "gwp-BCFM",
              "combinable": false,
              "inclusion": "{% assign today = \"now\" | date: \"%m-%d\" %}\n{% if '11-28, 11-29, 11-30, 12-1, 12-2, 11-27' contains today %}\n  1\n{% endif %}",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.filter(item=>!['melin E-GIFT CARD'].includes(item.product_title) && !['GWP'].includes(item.properties?._source)).map(i=>i.final_line_price)) > 50000",
              "progress_numerator": "",
              "progress_denominator": "",
              "action_filter": "!cart.items.some(i=>!!i.properties._source==`GWP`)",
              "delay": 0,
              "actions": "Cart.add({\nid:39472223256672,\nproperties:{_source:`GWP`\n},quantity:1})",
              "revoke": "Cart.remove(cart.items.find(i=>i.properties._source === 'GWP').variant_id)",
              "automatic": true,
              "storage": "local",
              "approach": "",
              "success": "",
              "threshold_icon": "",
              "threshold_label": ""
            }
          }
        },
        "block_order": [
          "offer_JB4Wr9",
          "offer_T8UU3A",
          "offer_wPAwC6",
          "offer_yNfTp9",
          "offer_XJBGYJ",
          "offer_jTENab",
          "offer_t4ccQE"
        ],
        "settings": {}
      },
      "geolocation": {
        "type": "geolocation",
        "blocks": {
          "site_EwnGRP": {
            "type": "site",
            "settings": {
              "name": "CANADA",
              "current": false,
              "code": "CA",
              "countries": "CA",
              "url": "https://www.melinbrand.ca/",
              "redirect": false,
              "welcome": true
            }
          },
          "2cd445e6-21dd-4da3-bc4a-38d9c28a1130": {
            "type": "site",
            "settings": {
              "name": "UK + EUROPE",
              "current": false,
              "code": "EU",
              "countries": "ES,UK,FR",
              "url": "https://melinbrand.co.uk/",
              "redirect": false,
              "welcome": true
            }
          },
          "eb9a120a-af5c-4671-8c0f-cb27536e7286": {
            "type": "site",
            "settings": {
              "name": "US + INTERNATIONAL",
              "current": true,
              "code": "US",
              "countries": "US,*",
              "url": "",
              "redirect": false,
              "welcome": false
            }
          }
        },
        "block_order": [
          "site_EwnGRP",
          "2cd445e6-21dd-4da3-bc4a-38d9c28a1130",
          "eb9a120a-af5c-4671-8c0f-cb27536e7286"
        ],
        "custom_css": [
          ".geolocation__header {background-color: #ffffff;}"
        ],
        "settings": {
          "heading": "SELECT REGION",
          "currency_selector": false,
          "language_selector": false,
          "site_switcher": true,
          "site_list": true,
          "button_text": "Change Settings",
          "show_flag": "yes",
          "flag": "https://flagcdn.com/216x162",
          "geoip": "https://get.geojs.io/v1/ip/country.json"
        }
      },
      "globals": {
        "type": "globals",
        "settings": {}
      },
      "loyalty": {
        "type": "loyalty",
        "settings": {
          "title": ""
        }
      },
      "giftcard": {
        "type": "giftcard",
        "blocks": {
          "link_k9nUBk": {
            "type": "link",
            "settings": {
              "link_url": "",
              "link_text": ""
            }
          }
        },
        "block_order": [
          "link_k9nUBk"
        ],
        "settings": {
          "logo": "shopify://shop_images/melin_Logo_Horizontal.svg",
          "image": "shopify://shop_images/Gift_Card_Art_2x_1200x_bf4cc3c5-2b66-487a-ad67-a367f794368b.jpg"
        }
      },
      "product-badges": {
        "type": "product-badges",
        "blocks": {
          "badge_baXbrL": {
            "type": "badge",
            "settings": {
              "title": "Online Only",
              "tag": "Online-Only",
              "grid": "Online Only",
              "detail": "Online Only",
              "carousel": "Online Only",
              "search": "Online Only",
              "color": "#000000",
              "bg_color": "#ededed"
            }
          },
          "badge_9RxTRV": {
            "type": "badge",
            "disabled": true,
            "settings": {
              "title": "Free Shipping",
              "tag": "free-shipping",
              "grid": "Free Shipping",
              "detail": "Free Shipping",
              "carousel": "Free Shipping",
              "search": "Free Shipping",
              "color": "#000000",
              "bg_color": "#ededed"
            }
          },
          "badge_a4DRFD": {
            "type": "badge",
            "settings": {
              "title": "Limited",
              "tag": "limited-edition",
              "grid": "Limited",
              "detail": "Limited",
              "carousel": "Limited",
              "search": "Limited",
              "color": "#000000",
              "bg_color": "#ededed"
            }
          },
          "badge_NEYwzQ": {
            "type": "badge",
            "settings": {
              "title": "New",
              "tag": "new",
              "grid": "New",
              "detail": "New",
              "carousel": "New",
              "search": "New",
              "color": "#000000",
              "bg_color": "#ededed"
            }
          },
          "badge_zQMwXD": {
            "type": "badge",
            "settings": {
              "title": "Popular",
              "tag": "best-seller",
              "grid": "Popular",
              "detail": "Popular",
              "carousel": "Popular",
              "search": "Popular",
              "color": "#000000",
              "bg_color": "#ededed"
            }
          }
        },
        "block_order": [
          "badge_baXbrL",
          "badge_9RxTRV",
          "badge_a4DRFD",
          "badge_NEYwzQ",
          "badge_zQMwXD"
        ],
        "settings": {}
      },
      "customer-segments": {
        "type": "customer-segments",
        "settings": {}
      }
    },
    "content_for_index": [],
    "blocks": {
      "16274361319697914395": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-floating/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "widget_id": "",
          "show_all": true,
          "show_index": true,
          "show_product": true,
          "show_collection": true,
          "show_cart": true
        }
      },
      "10830403595879607806": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-core/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "url_key": "melin",
          "lang": "auto"
        }
      },
      "17428600031447253342": {
        "type": "shopify://apps/attentive/blocks/attn-tag/8df62c72-8fe4-407e-a5b3-72132be30a0d",
        "disabled": false,
        "settings": {}
      },
      "7079518687644006178": {
        "type": "shopify://apps/checkout-blocks/blocks/app-embed/b0024ca5-9de8-4bf0-a92f-cd0783877ae2",
        "disabled": false,
        "settings": {
          "utm_id": true,
          "utm_source": true,
          "utm_medium": true,
          "utm_campaign": true,
          "utm_term": true,
          "ref": true
        }
      },
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      },
      "1269195111575427461": {
        "type": "shopify://apps/keepcart-stop-coupon-leaks/blocks/keepcart/6c9b264a-3284-48f2-a6f6-2836be6ada2a",
        "disabled": false,
        "settings": {}
      },
      "12439146293631897289": {
        "type": "shopify://apps/blockify-fraud-blocker/blocks/app_embed/2e3e0ba5-0e70-447a-9ec5-3bf76b5ef12e",
        "disabled": false,
        "settings": {}
      },
      "15316669992541710076": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-rating-snippet/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "star": "#f1a307",
          "text_color": "rgb(0,0,0)",
          "snippet_mode": "default",
          "text": "",
          "line_break": false,
          "listen_for_changes": true,
          "rating_without_brackets": false,
          "format_review_count": false,
          "show_popup": false,
          "anchor_to_reviews_widget": true,
          "show_empty_stars": true
        }
      },
      "1877152918344802950": {
        "type": "shopify://apps/intelligems-a-b-testing/blocks/intelligems-script/fa83b64c-0c77-4c0c-b4b2-b94b42f5ef19",
        "disabled": true,
        "settings": {}
      },
      "206405091821406056": {
        "type": "shopify://apps/shop-with-friends/blocks/polls_floating/3e557a13-d1da-4545-a899-617798d1a177",
        "disabled": false,
        "settings": {}
      }
    }
  },
  "type": "template",
  "platform_customizations": {
    "custom_css": []
  }
}
