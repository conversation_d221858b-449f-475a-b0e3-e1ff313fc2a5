@import '../../components/product-item/theme-melin.scss';
@import '../../components/product-finder/theme-melin.scss';
@import '../../components/modal/theme.scss';
@import '../../components/search-suggestion/search-suggestion.scss';
@import '../shared/styles/footer.scss';
@import '../shared/styles/quick-add.scss';


/* Header
========================================================================== */
// html,body {
// 	overflow-x:hidden !important;
// } 

.header-bar {
	&--main-promo {
		@apply py-2 text-sm;
		.header-bar__container {
			@apply justify-center items-center;
		}

    .announcement {
      @apply font-body text-[13px] font-normal tracking-[0.4px];
    }
	}
	&--main {
		@apply bg-white border-b;
		.header-bar__container {
			@apply w-full justify-start items-center lg:px-[30px] px-[20px];
			
			span.header-bar__block--menu {
				@apply lg:pb-0 pb-9; 
			}
		}

		.header-bar__block:not(.header-bar__block--menu) {
			height:60px;
		}
	}
	&__block {
		&--logo {
			@apply mr-[34px];

		}
		&--menu {
			height:auto;
			background:#fff;
			@apply max-lg:order-last bg-white max-lg:-mx-5 max-lg:w-screen; 
			
			> ul.flex-row {
				> li {
					> a, summary {
						height:60px;
						@apply flex items-center px-base relative overflow-hidden;

						&:after {
							@apply bg-primary;
							height:3px;
							position:absolute;
							bottom:0;
							transform:translateY(100%);
							transition:transform 200ms ease;
              left:17px; // match padding to center
							width:calc(100% - 34px);
							content:'';
						}

						&:hover {
							&:after {
								transform:translateY(0%);
							}
						}
					}
					details {
						@apply h-full;

						&[open] {
							summary:after {
								transform:translateY(0%);
							}
						}
					}
				}
			}

			> ul.flex-col {
				line-height:26px;
				details {
					summary {
						@apply p-base items-center border-b border-tertiary;
					}
					ul {
						details {
							summary {
								a {
									@apply font-body text-base font-normal;
								}
								& ~ ul li a {
									@apply font-body text-[13px] font-normal py-0;
								}
							}
							ul {
								@apply  pb-base px-base;
								li{
									@apply px-base;
									a {
										font-size: 13px;
									}
								}
							}
							&[open] {
								summary{
									@apply border-transparent;
								}
								ul {
									@apply border-b border-tertiary;
								}
							}
						}	
					}
				}
				.header-nav__item {
					& > a {
						@apply p-base block;
					}
					.type-nav-link {
						@apply font-body font-normal capitalize tracking-[0.4px] text-base;
					}
				}
			}

			.menu-item {
				@apply border-r border-tertiary last:border-r-0;
				&__menu {
					@apply px-xl;
				}
				ul {
					li {
						&:has(a + ul) {
							> a {
								@apply lg:mb-2;
							}
						}
						& > ul > li {
							a {
								@apply pt-2;
							}
						}
					}
				}
			}
		}
		
		&--nav-tools {
			@apply justify-end lg:gap-[25px];
			height:60px;
			svg {
				stroke-width:1.5px;
				@apply text-primary;
			}
			.nav-tools__account-greeting {
				font-size:12px;
			}
			.nav-tools__cart-count {
				position: absolute;
		    top: -4px;
		    right: -10px;
				display:flex;
				justify-content:center;
				align-items:center;
				width:20px;
				height:20px;
				font-size:12px;
				border-radius:100%;
        @apply bg-primary text-white;
			}
			button,a {
				position:relative;
			}
		}
		&--menu-toggle {
			margin-left:-20px;
			button {
				padding:1rem;
				.icon {
					stroke-width: 2;
				}
			}
		}

		&--search {
			.page-scroll--down:not(.page-scroll--top) & {

				@media only screen and (max-width: 1023px) {
					position: absolute;
			    z-index: 50;
			    width: calc(100% - 6rem);
			    right: 3rem;
					top: 0;
			  }

			}
		}

	}
	&__search-field {
		label {
			@apply bg-[#f8f8f9] px-sm rounded-full;
			.icon {
				width:18px;
			}
			input {
				@apply bg-[#f8f8f9] p-[6px] text-[12px] w-full;
				&:focus {
					outline:none;
				}
			}
		}
	}
}

@media only screen and (max-width: 1023px) {
	.header-bar {
		&--main {
		}
		&__block {
			&--menu{
				border-bottom:1px solid #eee;
			}
			&--logo, &--menu-toggle, &--nav-tools {
				.header-bar--main & {
					position:sticky;
					top:0;
					z-index:41;
				}
			}
			&--logo {
				.header-bar--main & {
					flex-grow:1;
					margin-right:0;
				}
			}
			&--menu-toggle {
				&:before {
					z-index:40;
					pointer-events:none;
					position:absolute;
					top:0;
					left:0;
					height:100%;
					width:100vw;
					background:#fff;
					border-bottom:1px solid #eee;
					content:'';
				}
				button{
					z-index:41;
				}
			}
		}
		&--main {
			/* max-height:calc(100dvh - 36px); */
			max-height: 100dvh;
			overflow: auto;
		}
		
	}
}

@media only screen and (min-width: 1023px) and (max-width: 1300px) {
	span.header-bar__block.header-bar__block--logo.h-full.flex.items-center {
		width: 22%;
		justify-content: flex-start;
	}
	.header-bar__block--nav-tools {
		gap: 12px;
	}
	.header-bar--main .header-bar__container {
		padding-left: 20px;
		padding-right: 20px;
	}
}
@media only screen and (min-width: 1301px) {
	span.header-bar__block.header-bar__block--logo.h-full.flex.items-center{
		margin-right: 12.5rem;
	}
}



	@media only screen and (max-width: 425px) {
		.header-bar__block--menu>ul.flex-row>li>a, .header-bar__block--menu>ul.flex-row>li summary {
			padding-left: 6px !important;
			padding-right: 10px !important;
		}
	}
	@media only screen and (max-width: 390px) {
		.type-nav-link {
			font-size: .70rem;
			}
	}


	@media only screen and (min-width: 1023px) {

#shopify-section-header {
	@media only screen and (max-width: 1023px) {
		max-height:100dvh;
		overflow-y:scroll;
	}
	&:before {
		pointer-events:none;
		position:fixed;
		z-index:0;
		content:'';
		background:rgba(0,0,0,0.5);
		top:0;
		left:0;
		right:0;
		height:100vh;
		opacity:0;
		transition:opacity 600ms ease;	
	}
}
	}
.menu-item {	
	ul {
		li {
			a {
				@apply flex pt-0;
				font-size: 22px;
				font-family: var(--font-medium);
				line-height: 24px;
			}

			& > ul > li {
				a {
					@apply text-[13px] font-body pt-2.5;
				}
			}
		}
	}
}

.nav-item {
	@apply text-center rounded overflow-hidden;
	img {
		transition:scale 200ms ease;
	}
	&:hover {
		img.nav-item__media {
			scale:1.1
		}
	}
	&__text {
		@apply text-[13px] font-heading uppercase tracking-[0.4px];
	}
}


/* Footer
========================================================================== */

#shopify-section-footer {
  @apply border-t-[5px] border-primary text-white;

	.menu-item {
		@apply p-xl max-lg:border-b max-lg:border-white;

		ul {
			li {
				&:has(a + ul) {
					> a {
						@apply lg:mb-[18px];
					}
	
					&.menu-active {
						> a {
							@apply mb-3;
						}
					}
	
					button {
						@apply max-lg:grow max-lg:flex max-lg:justify-end;
					}
				}
			}
		}
	}
	
  .footer__credits {
    @apply flex-col items-start px-xl pb-xl lg:pb-2xl lg:pt-0 lg:mt-0 lg:px-6xl lg:border-0;
  }

  .footer__credit--copyright {
    @apply flex flex-col gap-1;
  }

  .footer__credits,
  .footer__credit--copyright,
  .footer__credit--links {
    @apply gap-1 text-left;
    .footer__credit--link {
      &:not(:last-child):after {
        content: " | ";
        padding: 0 4px;
      }
    }
  }
  
  .footer__credit--links {
    @apply flex-wrap justify-end;
  }
 
  .content-item__text-stack .type-item {
    @apply font-heading text-white block pt-0;
		font-size: 22px;
		font-family: var(--font-medium);
		line-height: 24px;
  }
}



/* Newsletter
========================================================================== */

.section--newsletter {	
	@apply text-left lg:text-center;

	.newsletter__title {
		@apply mt-0 max-lg:px-2.5 mb-2.5;
	}

	.newsletter__subtitle {
		@apply max-lg:px-2.5 mb-10 lg:mb-8;
	}

	.section__mini-forms {
		@apply flex justify-center divide-black max-lg:divide-y max-lg:flex-col max-lg:items-center lg:divide-x;
	}

	.mini-form {
		@apply px-2.5 py-6 lg:px-8 lg:max-w-[360px];

		input {
			@apply border-black font-body;
		}
	}

	.section__mini-forms [hidden="hidden"] + script + div {
		border: 0;
	  }

	summary, input {
		@apply lg:max-w-[360px];
	}

	.newsletter__error {
        display: none;
        color: #FF0000;
        font-family: GTA-Bold,Arial,Helvetica,sans-serif;
        font-size: 12px;
        margin-bottom: 2px;
        margin-top: -2px;
        padding: 12px 20px 15px;
        text-align: center;
    }
}

/* Article List
========================================================================== */

.section--article-list {
	.section {
		&__title {
			@apply mt-0 pb-0 mb-2xl;
		}
	}
}

/* Article
========================================================================== */
.article {
	@apply lg:pb-8;

  &__header {
    @apply relative z-10 max-w-none w-full lg:max-w-[52rem] lg:mt-[-5rem] lg:px-0 px-6 pt-16 border-t-8 border-primary;

    & > * {
      @apply lg:max-w-2xl lg:mx-auto;
    }

		.article__title {
			font-family: var(--font-medium);
			@apply max-lg:text-2xl;
		}
  }

  &__content,
	&__footer {
   @apply lg:max-w-2xl lg:mx-auto max-lg:px-6;
  }

	&__footer {
		@apply relative z-10 max-w-none w-full lg:max-w-[52rem] pb-8 lg:px-0 px-4 border-b-8 border-primary;
  }

	&__link {
		font-family: var(--font-medium);
		@apply mt-4;
	}

	&__content {
		font-size: 16px;
    letter-spacing: -0.12px;
		line-height: 25px;
    @apply font-body;
  }

  &__info {
    @apply justify-start mb-5 flex-wrap flex gap-y-2;

    & > * {
      @apply text-[10px] font-body tracking-widest uppercase;

      &:not(:last-child) {
        @apply mr-3 pr-3 border-dark border-r;
      }
    }
  }

	.social-sharing {
		@apply mb-4;
	}

	&__category,
	&__tag {
		@apply opacity-40;
	}

	&-video {
		&__eyebrow {
			@apply mb-8;
		}

		&__category,
		&__date {
			@apply text-[11px] text-[#736b67] uppercase;
		}

		&__rte {
			h1:first-of-type,
			h2:first-of-type,
			h3:first-of-type  {
				@apply mt-0;
			}
			p {
				@apply font-body text-[15px] leading-[1.45455] -tracking-[0.12px];
			}
		}
	}
}

.template-article {
	#MainContent {
		@apply bg-light;
	}
}


/* Review Item
========================================================================== */

.review-item {
	&__product {
		@apply text-base pb-2 font-normal;
		font-family: var(--font-medium);
	}

	&__type {
		@apply font-normal font-body text-[13px] text-[#77706c];
	}
}


/* Content Carousel
========================================================================== */

.section--content-carousel {
	.type-section {
		@apply m-0;
	}
	.product-header {
		@apply mb-lg;
		&__titles {
			@apply lg:flex;
			align-items:center;
			gap:1rem;
		}
		&__title {

		}
		&__subtitle {
			color:#000000;
			font-size: 12px;
			font-family: var(--font-regular);
		}
		@media only screen and (max-width: 767px) {
			&__titles {
				align-items: flex-start;
				display: flex;
				gap: 0rem;
				flex-direction: column;
			}
		}
	}

	.content-item {
		&--carousel-header {
			@apply flex-wrap mb-10;
			
			.content-item__content {
				@apply gap-y-4;
			}
		}
		&--review {
			.content-item {
				&__review {
			    line-height: 1.2;
					@apply line-clamp-2;
				}
				&__author {
					line-height: 16.9px;
					font-size: 13px;
					margin-top: 13px;
				}
			}
		}
	}
}


/* Search Results
========================================================================== */

.search-results {
	@apply px-0 pb-2.5 pt-0 bg-light text-body;

  &__header {
    @apply p-2.5;
    display:none;
  }

  &__type {
  	@apply px-sm;
  	font-size:14px;
  }

	&__type-label {
		@apply bg-light mb-0 py-2.5 px-2.5 text-center text-base;
	}


	&__item--product {
		a {
			@apply flex items-start gap-2.5;
		}

		img {
			@apply w-auto h-auto max-w-[70px] max-h-[70px];
		}
	}
}

.search-result-item {
	@apply bg-white p-2.5 border-l border-r border-t border-gray-200 ;


	&:first-of-type {
		@apply rounded-t;
	}
	&:last-of-type {
		@apply border-b border-gray-200 rounded-b;
	}

	&__title {
		@apply text-[14px] leading-tight mb-2;
	}

	&__type {
		@apply mb-2;
	}

	&__type,
	&__sku {
		font-size: 13px;
    color: #736b67;
    letter-spacing: 0;
    margin-bottom: 8px;
	}
	
	&__price {
		@apply text-[15px];
	}
}

/* Slider Cart
========================================================================== */

.modal--slider-cart {
	background-color: #f5f5f5;
}

.slider-cart {
	@apply lg:max-w-[500px] lg:w-full;

  &__header {
    @apply bg-black text-white py-8 font-body;
  }
  
  .offer {
	// color:#042c4b;
&.offer--combinable {
  @apply pb-[18px];

  .progress {
	&--fill {
	  background: linear-gradient(to right, var(--progress-fill-color), transparent) !important;
	}
	&--last-tier {
	  background: var(--progress-fill-color) !important;
			}
  	}
	}
  }	

	// &__recs {
	// 	&-title {
	// 		@apply px-4 sm:px-6;
	// 	}
	// }
}

.offer-summary {
	@apply flex flex-col-reverse gap-2 w-full;
	&__item {
	  @apply flex items-center justify-between gap-2;
	}
	&__header {
	  @apply flex items-center gap-2;
	}
	&__icon {
	  @apply w-[13px] h-[13px] p-[3px] rounded-full;
	}
	&__title {
	  font-family: var(--font-body-family);
	  font-size: 12px;
	}
	&__value {
	  font-family: var(--font-heading-family);
	  font-size: 13px;
	}
  }
  
  
  .progress {
	&__threshold-markers {
	  @apply absolute w-full top-1/2 -translate-y-1/2 left-0 flex justify-between items-start;
	}
	&__threshold-marker {
	  @apply flex flex-col items-center justify-center w-8 relative; 
	  &-icon {
		@apply w-8 h-8 mb-1 p-[5px] rounded-full;
	  }
	  &-label {
		@apply text-[9px] leading-none text-center w-[50px] overflow-visible absolute top-[36px];
	  }
	}
	&--fill {
	  @apply pb-1.5 relative;

	}
  }


.slider-cart {
	&__recs {
		padding-bottom: 1.44rem;
    	padding-top: 1.44rem;	
		&-title {
			margin-top: 0.833rem;
			margin-bottom: 0.833rem;
			padding-left: 1.44rem;
			padding-right: 1.44rem;
			color: #000;
			font-family: 'Avenir-Medium', sans-serif;
			font-size: 14px;
			line-height: 1;
			text-transform: capitalize;
		}
	}
}

.cart {
	&__checkout-button {
		.button {
			@apply hover:bg-black hover:border-black;
			height: 52px;
		}
	}
	&__item-wrapper {
		@apply bg-[#f5f5f5] px-0;
		#summary-heading {
			@apply text-[18px] capitalize;
		}

		.button {
			height: 52px;
		}
	}
}


.cart-item {
	@apply px-[16px] lg:px-[24];
	background-color: #ffffff;
	&__title {
		font-family: var(--font-medium);	
		line-height: 21px;
		@apply mb-[3px];
	}

	&__line-item {
    @apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-[3px] order-3 col-start-1;
  	line-height: 15.6px;
	}

	&__badge {
		@apply font-subheading;
	}
	
	&__quantity {
		@apply bg-white rounded-[50px] w-auto px-4 py-[2px] border mb-0;
		svg {
			stroke-width: 1px;
		}
		input {
		  font-size: 13px !important;
		  @apply font-heading;
		}
		button {
			height: auto;
			width: auto;
		}
	  }

	  &__badges {
		@apply flex justify-start;
	  }
	  &__badge {
		@apply p-2 text-[12px] leading-none font-heading;
	  }


}

/* Product Essentials
========================================================================== */

.product-essentials {
	&__media-item {
		img {
			// @apply lg:bg-[#f5f5f5];
		}
	}

	&__media-play {
		@apply  bg-tertiary gap-1 px-2 h-5 rounded-full bottom-8 right-4 items-center z-10;
		span {
			@apply uppercase text-xs font-heading flex leading-none mt-0.5;
		}
		svg {
			@apply fill-black stroke-transparent;
		}
	}
    &__modal-close {
        @apply bg-white;
        color: #373F47;
    }
}

.payments-banner {
	@apply pt-base;
}


/* Product Form
========================================================================== */

.product-form {
	&__option {
		@apply mb-8 last:max-lg:mb-0 -mx-lg;

		.field__buttons {
			@apply grid px-lg;
		}

		

		&--color {
			@apply overflow-hidden lg:overflow-auto;

			.field__buttons {
				@apply flex flex-row overflow-x-auto lg:grid;
			
				-ms-overflow-style: none;  /* IE and Edge */
				scrollbar-width: none;  /* Firefox */
				
				&::-webkit-scrollbar {
					display: none;
				}
			}

			.field__button {
				@apply w-[92px] h-auto grow-0 basis-auto shrink-0 lg:w-full lg:h-full lg:grow lg:shrink border border-tertiary p-0 rounded-[3px];

				&.unavailable {
					@apply bg-transparent text-body;
				
					&:after {
						content: '';
						background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' preserveAspectRatio='none' viewBox='0 0 100 100'><line x1='0' y1='100' x2='100' y2='0' stroke='%23e4e4e4' stroke-width='2' vector-effect='non-scaling-stroke'/></svg>");
						background-repeat: no-repeat;
						background-size: cover;
						@apply w-full h-full absolute top-0 left-0;
					}
				}

				&-text {
					@apply hidden;
				}

				&:focus,
				&:has(input:checked) {
					@apply border-dark border;
				}

				img {
					@apply object-contain aspect-1;
				}

				aspect-ratio: 1 / 1;
			}
		}

		&:not(&--color) {
			.field__buttons {
				@apply flex flex-wrap lg:grid lg:grid-cols-3;
			}

			.field__button {
				@apply basis-[3.5rem] shrink grow lg:grow-0 h-12 border-tertiary rounded-[3px];

				&.unavailable {
					@apply bg-[#eee] border-[#eee];
					
					input:checked	~ .field__button-text {
						@apply bg-white;
					}

					.field__button-text {
						@apply text-[#38130033];
					}
				}

			
				
				.field__button-text {
					@apply font-body text-sm;
				}

				&:hover {
					.field__button-text {
						@apply border-dark border;
					}
				}
			}
		
			img {
				@apply hidden;
			}
		}

		&-selected {
			@apply mb-2.5 px-lg font-body text-dark text-[13px] font-normal tracking-normal;
		}
	}
	.button.button--primary {
		height: 52px;
	}
}

.field__button {
  @apply basis-full;
	&-text {
		@apply rounded-[3px];
	}
}

.field__buttons {
  &--tight {
    @apply gap-2;
  }
}


/* Product Header
========================================================================== */

.product-header {
	@apply w-full;
	
  &__top {
		@apply mb-0 flex items-start;
  }
	&__bottom {

	}
	&__price,
	&__compare-at-price {
		@apply text-[20px] font-body uppercase tracking-[.05rem];
	}
  &__type {
    @apply font-body text-[15px] font-normal text-[#736b67] tracking-normal;
  }
	&__prices {
		@apply flex-row-reverse flex max-lg:flex-wrap gap-2;
	}
	&__title {
		@apply lg:text-[22px] text-[20px] capitalize tracking-[-.38px] lg:leading-[33px] leading-[29px];
		font-family: var(--font-regular);
	}
	&__description {
		@apply hidden lg:block font-body mb-[25px] leading-[1.125];
	}
	&__breadcrumbs {
		@apply font-body text-[0.75rem];
	
		a {
			&:last-child {
				@apply text-dark
			}
		}
	}
	
}

.product-swatch-color-tooltip{
    visibility: hidden;
    text-align: center;
    position: absolute;
    z-index: 1;
    margin-bottom: 14px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    max-width: none;
    height: 36px;
    background: rgb(255, 255, 255);
    color: rgb(51, 51, 51);
    letter-spacing: 0.025rem;
    border-radius: 50px;
    padding: 8px 16px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 16px -4px;
    font-size: 0.75rem;
    font-weight: normal;
    bottom: 100%;
    white-space: nowrap;
}

label.field__button:hover .product-swatch-color-tooltip{
    visibility: visible !important;
}
@media screen and (max-width: 1024px) {

	.collection-title-shop-all {
		margin-bottom: 35px;
    	margin-top: 27px;
	}

	.button-desktop{
		display: none;
	}
	.button-mobile{
		display: block;
	}
}
@media screen and (min-width: 1024px) {

.button-mobile{
	display: none;
}
}

/* Reviews
========================================================================== */


.ElementsWidget-prefix {
	.ElementsWidget {
		.R-TextHeading {
			font-family: var(--font-medium);
		}
		
		.R-TextBody {
			@apply font-body font-normal;
		}
	
		.item__slidersGroup {
			@apply font-normal;
		}
		
		.footer__reviewsLogo-container {
			img.R-PlatformLogo,
			img.R-PlatformIcon {
				@apply hidden;
			}	
		}

		.header__inner {
			@apply px-0 lg:pt-5;
		}

		.ElementsWidget__search {
			.R-Field__input {
				&:focus {
					@apply focus:border-2 focus:border-black transition-all duration-200 ease-in-out;
				}
			}
		}

		.R-TabControls {
			.R-TabControls__item,
			.R-TabControls__item.isActive {
				@apply text-body;
			}
			.R-TabControls__item.isActive {
				@apply border-body;
			}
		}

		.R-Button {
			@apply font-body;

			&.R-Button--secondary {
				@apply bg-tertiary text-body;
			}
		}

		.R-PaginationControls {
			.R-PaginationControls__item {
				@apply text-body;

				&.isActive > .R-TextHeading {
					@apply text-body;
				}

				&.isActive {
					@apply border-b-body;
				}
			}
		}
	}
}


/* Product Meta
========================================================================== */
.product-meta {
	@apply gap-y-1;
  &__title {
    @apply text-[14px];
		
  }

  &__type {
    @apply text-dark font-body text-[12px];
		font-weight: 400;
  }

  &__color {
    @apply font-body text-dark text-[12px];
  }

  .reviews-snippet {
    @apply mb-1;
  }

  .product-item__prices {
    @apply mt-0;
  }
  .product-item__price {
    @apply font-body;
  }
}


/* Promo Motivator
========================================================================== */

.promo {
	@apply flex items-center gap-x-4 py-3;

	&__icon-wrapper {
		@apply shrink-0;
	}
	&__icon {
		@apply max-w-[48px];
	}
	&__content {
		@apply text-dark text-sm leading-none;
	}
	&__headline {
		@apply text-sm inline-block my-0;
		font-family: var(--font-medium);
	}
	&__description {
		@apply font-body text-[12px];
	}
}


/* Fit Guide
========================================================================== */

.fit-guide {
	.grid {
		@apply mb-8;
	}

	&__block-title {
		@apply text-base;
	}

	&__title {
		@apply font-heading text-sm;
	}

	.progress-bar {
		@apply mt-1;
	}
}


/* Collection
========================================================================== */

.template-collection {
  #MainContent {
    @apply bg-light;
  }
}

.collection {
	@apply lg:pt-4xl;

	&__sidebar {
		@apply lg:sticky lg:top-7xl;
		max-height: 100%;
		&:not([open]) {
			@apply z-0;
		}
		&[open="true"] {
			@apply z-[60];
		}

		&-header {
			@apply bg-[#ededed] lg:hidden;
			
			.label,
			button {
				@apply py-base px-lg gap-2 text-sm;

				span {
					@apply text-sm;
				}

				.icon {
					@apply w-4 h-4;
				}
			}
			&--filtered {
				.apply {
					@apply bg-black text-white;
				}
			}
		}

		&-body {
			position: static;
			overflow-x: hidden;
			// @apply py-lg px-md text-sm flex-col gap-sm overflow-y-auto lg:overflow-hidden;
			// height: 100% !important;
			@media (min-width: 768px) {
				padding-right: 10px;
			}
		}
	}

	&-filters {
		@apply divide-y-[1px] border-gray-200 gap-y-8 max-lg:order-1;

		&__accordion {
			@apply max-lg:px-lg;

			&-header {
				@apply pl-0 h-[54px] mb-2.5;
			}

			&-title {
				@apply font-body uppercase text-[14px] text-body tracking-widest;
			}

			&-content {
				@apply mt-2;
			}

			&-icon {
				.collection-filters__accordion:not([open]) & {
					transform:rotate(180deg);
				}
			}
		}

		&__content {
			@apply gap-y-[15px] pb-6;

			.field__buttons {
				@apply gap-1 max-lg:flex max-lg:flex-wrap lg:grid-cols-4;

				.field__button {
					@apply grow shrink-0 max-w-[50px];
				}
			}
		}

		& > .field {
			@apply lg:pt-5 pb-6;
		}

		.field {
			@apply mb-0;

			&__colors {
				@apply gap-x-2 gap-y-4;
			}

			&__color {
				@apply p-0;
			}
		}
	}
}

/* Quick Add
========================================================================== */
.modal--quickadd {
  padding: 30px 20px 20px;
	@apply text-body;
	.quick-add {
		&__info-button {
			@apply bg-black before:bg-black border-black before:border-black text-white before:text-white;
		}
		&__img-container {
			@apply bg-[#f5f5f5];
		}
	}
	
  .product-meta {
    .product-item__price {
      font-size: 14px;
			@apply mb-0;
    }
		&__footer {
			@apply flex flex-col gap-1;
		}
		.review-snippet {
			@apply order-last;
		}
  }

	.field__buttons {
		@apply flex flex-nowrap grow shrink;
		.field__button {
			@apply h-12;
		}
	}
}


/* Login Modal
========================================================================== */
.modal--account {	
	@apply w-80 rounded-lg p-8;
 
	.account-modal {
		&__title {
			@apply text-primary text-[24px] mt-4 mb-2;
		}
	
		&__subtitle {
			@apply text-primary text-[13px] mb-4;
		}
	
		&__button {
			@apply w-full mt-2;
		}

		&__link {
			@apply flex justify-center text-[13px] text-primary mt-4 cursor-pointer;
		}
	}
}



/* Sticky Stuff
========================================================================== */
body {
	&.page-scroll--up {
		&.template-collection {
			.section--sticky {
				&.active {
					@media only screen and (max-width: 767px) {
						top: 121px !important; //106
					}
				}
			}
		}
	}
}
.template-product{
	.section--sticky {
		&.active {
			@media only screen and (max-width: 767px) {
				// top:var(--header-offset);
				top:var(--header-bottom);
			}
		}
	}
}
body {
	&.page-scroll--up {
		&.template-product {
			.section--sticky {
				&.active {
					@media only screen and (max-width: 767px) {
						top: 121px !important;
					}
				}
			}
		}
	}
}
.section--sticky {
	opacity:0;
	&.active {
		opacity:1;
		top:var(--header-bottom);
		@apply shadow-lg;
		&.lg\:top-main {
			@media only screen and (min-width: 1024px) {
				top:var(--header-bottom);
		  }
		}
		// &.bottom-0 {
		// 	top:var(--header-offset);
		// }
		// @media only screen and (min-width: 1024px) {
		// 	top:var(--header-bottom);
	  	// }
	}
	.section__blocks {
		@apply gap-4;
	}
	.product-header__bottom {
		margin-bottom:0;
	}
	
	.section__block--gate:not(:has(.button)){
		display:none;
	}

  transition:transform 300ms ease;
  will-change: transform;
	
	&.active {
	  transform:translateY(0%)!important;
	} 

	&.bottom-0 {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(100%);
	  }
	}
	&.top-main {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(-100%);
	  }
	}

	&.lg\:bottom-0 {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(100%);
	  }
	}
	&.lg\:top-main {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(-100%);
	  }
	}
	
}


/* Geolocation
========================================================================== */
.geolocation {
	&__header {
		@apply border-b-0 pb-[15px] pt-[41px] text-center;
		p{
			@apply font-bold text-black text-base text-center uppercase;
			font-family: Avenir Black, Arial, Helvetica, sans-serif;
		}
	}

	&__footer {
		@apply p-xl;
	}

	article {
		@apply bg-light border-b;
		padding: 0 20px 10px;
	}

	.field:last-child {
		margin-bottom:0;
	}

	&__site_list_item {
		a {
			@apply flex items-center justify-center h-[39px] bg-black bg-opacity-100 text-white rounded border border-solid mb-2.5 p-1;
			&:hover {
				border-color:#CCC;
			}
			li:last-child & {
				margin-bottom:0;
			}
			img {
				height: auto;
				object-fit: contain;
				width: 25px;
				margin-right: 10px;
			}
			span{
				font-family: Avenir Black, Arial, Helvetica, sans-serif;
				font-size: 14px;
				font-weight: 600;
			}
		}
	}

	&__site_switcher {
		@apply p-xl gap-md;
	}
	&__site {
		@apply flex flex-col gap-xl items-center rounded-sm border mb-2 p-sm bg-white;
		&:hover {
			border-color:#CCC;
		}
	}
}

/* Subnav
========================================================================== */

.subnav {
	@apply overflow-hidden gap-x-4 bg-white border-t border-b border-gray-200 py-2 px-2.5 select-none;

  &__link {
    @apply border border-gray-200 rounded text-body block text-[13px] py-1.5 capitalize px-4 transition-all duration-300 ease-in-out text-center;
  }

  &__item {
    @apply opacity-100 pointer-events-none transition-opacity duration-[250ms];

    [id^='swiper-wrapper'] & {
      @apply opacity-100 pointer-events-auto;
    }

		&:hover,
    &.active {
      .subnav__link {
        @apply bg-primary border-primary text-white
      }
    }

    &:not(:first-child) {
      .subnav__link {
        @apply ml-1.5;
      }
    }
  }
  
  .swiper-slide {
    @apply w-auto;
  }
}


/* Account Pages
========================================================================== */

.section--account {
	@apply font-body;
	.order-detail {
		&__summary {
				@apply flex flex-col bg-[#ffffff]
				}
				}
	.section__header {
		.header__title {
			@apply capitalize text-2xl font-subheading;
		}
	}
	.section__sidebar {
		.sidebar {
			.sidebar__item {
				.sidebar__link {
					@apply text-[15px] font-bold hover:bg-transparent hover:underline-offset-8 hover:decoration-2 hover:underline;
				}	
				
				&:hover {
					.sidebar__link {
						text-decoration-color: currentColor;
					}	
				}
			}	
		}
	}
	.account-profiles {
		.account-profile {
			&__name {
				@apply text-[15px] font-body my-0;
			}
		}
	}
	.account-block,
	.wishlist {
		&__title {
			@apply font-heading text-[18px];
		}
	}
}

/* review widget
========================================================================== */
.reviews-io-ugc-widget {
	.MediaGalleryWidget-prefix {
		.MediaGalleryWidget {
			.MediaGalleryWidget__list-container {
				.MediaGalleryWidget__scrollButton--left {
					left: -5px;
				}
				.MediaGalleryWidget__scrollButton--right {
					right: -5px;
				}
			}
		}
	}
}

@media screen and (max-width: 1024px) {

	.desk-clear-all{
	  display: none;
	}
	}
	@media screen and (min-width: 1024px) {
	.desk-clear-all .clear-all-text  {
	  color: #381300;
	  size: 12px;
	  font-family: var(--font-medium);
	  border-bottom: 1px solid #381300;
	}
	.desk-clear-all {
	padding-bottom: 11px;
	}
	}

	
	.field__button .sr-only-hide[type="checkbox"]:focus + span, .field__button .sr-only-hide[type="checkbox"]:focus + span{
		outline: #381300 solid 2px;
	}
	
	.field__color .sr-only-hide[type="checkbox"]:focus + span, .field__color .sr-only-hide[type="checkbox"]:focus + span{
		outline: #381300 solid 2px;
	}

	.field__color .sr-only-hide[type="checkbox"],.field__button .sr-only-hide[type="checkbox"]{
		width: 0px;
		height: 0px;
		padding: 0px;
		margin: 0px;
		opacity: 0;
		border:none;
		z-index:-1 !important;
		appearance: none !important;
	}



/* Filtered Content
========================================================================== */

.section--filtered-content {
	@apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
	[steps] {
		article {
			padding: 0 0 34px;
			margin: 28px 0 0;
			@media (min-width:1024px){
				padding: 0 0 6rem;
				margin: 50px 0 0;
			}
			&:first-child {
				margin: 0;
				border-bottom: 1px solid #e3e3e3;
			}
		}
	}
	article[result] {
    border-top: 1px solid #e3e3e3;
	}
	.grid-cols-2 {
		.field__image--horizontal {
			@media (max-width:1024px){
				grid-column: span 2 / span 2;
			}
		}
	}
	#FilteredContentSearch {
		@apply flex items-center flex-col;
		max-width: 530px;
    margin: 41px auto 60px;
    position: relative;
    input {
    	border: none;
	    border-bottom: 1px solid var(--color-body);
	    border-radius: 3px;
	    color: var(--color-body);
	    font-family: var(--font-body-family);
	    width: 100%;
	    font-size: 16px;
	    line-height: 18px;
	    letter-spacing: -.2px;
	    padding: 11px 11px 11px 30px;
	    background: 0 0;
	    @media (min-width:1024px){
	    	font-size: 14px;
        padding: 11px 11px 11px 40px;
	    }
    }
    .icon-search {
    	stroke-width: 3px;
	    position: absolute;
	    width: 18px;
	    top: 50%;
	    right: 8px;
      transform: translateY(-50%);
	    @media (min-width:1024px){
	    	font-size: 14px;
	    	right: inherit;
	    	left: 8px;
	    }
    }
    > p {
	    font-size: 13px;
	    line-height: 18px;
	    color: #736b67;
	    margin: 30px 0 0;
    }
	}
	.search-results {
		top: 41px;
		width: 100%;
		max-height: 380px;
		height: auto;
		z-index: 10;
		padding: 0;
		background: #fff;
		border: 1px solid rgb(229, 231, 235);
		border-radius: 6px;
		.search-results__type-label {
			display: none;
		}
		.search-result-item {
			border-right: 0;
			border-left: 0;
			&:first-of-type {
				border-top: 0;
			}
			&:last-of-type {
				border-bottom: 0;
			}
		}
		.search-result-item__title {
			font-size: 15px;
	    line-height: 19px;
	    margin: 0 0 9px;
	    color: var(--color-body);
	    font-family: var(--font-body-family);
		}
		.search-result-item__type, .search-result-item__sku {
			display: block;
	    font-size: 13px;
	    line-height: 15px;
	    color: #736b67;
	    margin: 0 0 10px;
		}
		.search-result-item__price {
			font-size: 15px;
	    line-height: 18px;
	    color: #3a1603;
	    letter-spacing: 0;
	    font-family: var(--font-heading-family);
		}
	}
}

/* Segment Tabs
========================================================================== */

.variant-tabs {

  &__nav {
    @apply justify-evenly mb-6 mx-lg;
  }

  &__tab {
    @apply text-center justify-center flex-1 whitespace-nowrap text-ellipsis;
  }

}

.product-form__option-label-wrapper {
  @apply items-center mb-2.5;

  .product-form__option-selected {
    @apply m-0;
  }

  .button {
    @apply h-auto;
  }

  .product-form__option--color & {
    @apply uppercase;
  }
}

/* Tabs
========================================================================== */

.tabs, .tabs .field__toggle {
  @apply flex bg-white border border-[#eee] rounded-[4px] mb-5 divide-x overflow-hidden;

  span,
  a,
  button {
    @apply text-xs leading-relaxed font-normal cursor-pointer uppercase text-center py-3 px-3.5 bg-transparent whitespace-nowrap overflow-hidden relative;
    font-family: var(--font-body-family);
    transition: all 0.2s ease;
  }

  span,
  button.active,
  label input:checked+.toggle__label {
    @apply bg-black text-white;
  }

  a,
  a:not(.active),
  button:not(.active),
  label input:not(:checked)+.toggle__label {
    color: #0E1311;

    &:hover {
      color: #111827;
    }
  }

  label {
    span.toggle__label--unselected {
      display: none;
    }
  }
}